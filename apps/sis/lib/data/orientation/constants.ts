import { 
  ArrowRightIcon,
  CheckCircleIcon,
  ClipboardDocumentListIcon, 
  MapIcon,
  ReceiptPercentIcon,
  UserCircleIcon, 
  WifiIcon
} from "@heroicons/react/24/outline"

import { 
  ArrowBigRight,
  CalendarDays,
  CircleCheckBig,
  CircleUser,
  ClipboardList,
  MapPinned,
  ReceiptText,
  Wifi
} from "lucide-react"

export const videos = [
  {
    id: "communication",
    keyword: "Communication",
    title: "Communication & Finances",
    desc: "",
    link: "/orientation?id=communication",
    video: "**********",
    image: "/images/orientation/communication.jpg"
  },
  {
    id: "health",
    keyword: "Health",
    title: "Health & Well-being",
    desc: "",
    link: "/orientation?id=health",
    video: "**********",
    image: "/images/orientation/health_wellbeing.jpg"
  },
  {
    id: "housing",
    keyword: "Housing",
    title: "Housing",
    desc: "",
    link: "/orientation?id=housing",
    video: "**********",
    image: "/images/orientation/housing.jpg"
  },
  {
    id: "landmarks",
    keyword: "Landmarks",
    title: "Landmarks",
    desc: "",
    link: "/orientation?id=landmarks",
    video: "**********",
    image: "/images/orientation/landmarks.jpg"
  },
  {
    id: "safety",
    keyword: "Safety",
    title: "Safety",
    desc: "",
    link: "/orientation?id=safety",
    video: "**********",
    image: "/images/orientation/safety.jpg"
  },
  {
    id: "shopping",
    keyword: "Shopping",
    title: "Shopping & Food",
    desc: "",
    link: "/orientation?id=shopping",
    video: "**********",
    image: "/images/orientation/shopping.jpg"
  },
  {
    id: "taxi",
    keyword: "Taxi",
    title: "Taxis in Amman",
    desc: "",
    link: "/orientation?id=taxi",
    video: "**********",
    image: "/images/orientation/taxi.jpg"
  },
  {
    id: "visa",
    keyword: "Visa",
    title: "Visas & Formalities",
    desc: "",
    link: "/orientation?id=visa",
    video: "1053273658",
    image: "/images/orientation/visa.jpg"
  }
]

export const onsiteProgress = [
  {
    id: "1",
    keyword: "attendance",
    title: "CONFIRM ATTENDANCE",
    desc: "Click here to confirm your attendance",
    icon: CircleUser,
    completed: CircleCheckBig
  },
  {
    id: "2",
    keyword: "policy",
    title: "STUDENT POLICY",
    desc: "Click here to sign your student policy form",
    icon: ClipboardList,
    completed: CircleCheckBig
  },
  {
    id: "3",
    keyword: "tuition",
    title: "PAY TUITION",
    desc: "Click here to pay your 350 USD tuition deposit",
    icon: ReceiptText,
    completed: CircleCheckBig
  }
]

export const onlineProgress = [
  {
    id: "1",
    keyword: "deposit",
    title: "PAY TUITION",
    desc: "Click here to pay your 50 USD deposit fee",
    icon: ReceiptText,
    completed: CircleCheckBig
  },
  {
    id: "2",
    keyword: "placement",
    title: "RESERVE YOUR PLACEMENT",
    desc: "Click here to reserve a placement exam date",
    icon: CalendarDays,
    completed: CircleCheckBig
  }
]

export const progress = [
  {
    id: "1",
    keyword: "attendance",
    title: "CONFIRM ATTENDANCE",
    desc: "Click here to confirm your attendance",
    icon: UserCircleIcon,
    completed: CheckCircleIcon
  },
  {
    id: "2",
    keyword: "policy",
    title: "STUDENT POLICY",
    desc: "Click here to sign your student policy form",
    icon: ClipboardDocumentListIcon,
    completed: CheckCircleIcon
  },
  {
    id: "3",
    keyword: "tuition",
    title: "PAY TUITION",
    desc: "Click here to pay your 350 USD tuition deposit",
    icon: ReceiptPercentIcon,
    completed: CheckCircleIcon
  }
]

export const applicantActions = [
  {
    title: "Qasid Onsite | Amman",
    href: "/admissions/onsite",
    description: "Application Form (All Onsite Classes)",
    icon: MapPinned,
    iconForeground: "text-background",
    iconBackground: "bg-primary"
  },
  {
    title: "Qasid Online",
    href: "/admissions/online",
    description: "Online Arabic Coaching, One-on-One & Private Tutoring Classes",
    icon: Wifi,
    iconForeground: "text-background",
    iconBackground: "bg-primary",
    userType: "all"
  },
  {
    title: "Continuation Form",
    href: "/admissions/continuation",
    description: "Group Classes & Private Tutoring",
    icon: ArrowBigRight,
    iconForeground: "text-background",
    iconBackground: "bg-primary",
    userType: "all"
  }
]

export const standardActions = [
  {
    title: "Qasid Onsite | Amman",
    href: "/admissions/onsite",
    description: "Application Form (All Onsite Classes)",
    icon: MapPinned,
    iconForeground: "text-background",
    iconBackground: "bg-primary"
  },
  {
    title: "Qasid Online",
    href: "/admissions/online",
    description: "Online Arabic Coaching, One-on-One & Private Tutoring Classes",
    icon: Wifi,
    iconForeground: "text-background",
    iconBackground: "bg-primary",
    userType: "all"
  },
  {
    title: "Continuation Form",
    href: "/admissions/continuation",
    description: "Group Classes & Private Tutoring",
    icon: ArrowBigRight,
    iconForeground: "text-background",
    iconBackground: "bg-primary",
    userType: "student"
  },
  { title: "Information Form", 
    href: "/profile/", 
    description: "Please complete your student information",
    icon: CircleUser, 
    iconForeground: "text-background",
    iconBackground: "bg-primary",
    userType: "all"
  }
]
"use client"

import type React from "react"
import { useState, useEffect, useMemo, useCallback, useRef } from "react"
import { ChevronDown, Search, X } from "lucide-react"
import { cn } from "@/lib/utils"

import { Input } from "@/app/components/ui/input"
import { Button } from "@/app/components/ui/button"

interface Student {
  id: string | number
  value: string
  label: string
}

interface VirtualizedStudentSelectProps {
  students: Student[]
  selectedStudents: Student[]
  onStudentSelect: ( student: Student ) => void
  onStudentRemove: ( studentId: string | number ) => void
}

const ITEM_HEIGHT = 40
const MAX_VISIBLE_ITEMS = 10
const CONTAINER_HEIGHT = ITEM_HEIGHT * MAX_VISIBLE_ITEMS

export const VirtualizedStudentSelect = ({
  students,
  selectedStudents,
  onStudentSelect,
  onStudentRemove
}: VirtualizedStudentSelectProps ) => {
  const [isOpen, setIsOpen] = useState( false )
  const [searchTerm, setSearchTerm] = useState( "" )
  const [scrollTop, setScrollTop] = useState(0)
  const scrollElementRef = useRef<HTMLDivElement>( null )
  const searchInputRef = useRef<HTMLInputElement>( null )

  // Debounced search to improve performance
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState( "" )

  useEffect( () => {
    const timer = setTimeout( () => {
      setDebouncedSearchTerm( searchTerm )
    }, 300 )
    return () => clearTimeout( timer )
  }, [searchTerm] )

  // Filter students based on search term
  const filteredStudents = useMemo( () => {
    if ( !debouncedSearchTerm.trim() ) return students

    const searchLower = debouncedSearchTerm.toLowerCase()
    return students.filter( ( student ) =>
      student.label.toLowerCase().includes( searchLower ) 
    // || student.value.toLowerCase().includes( searchLower )
    )
  }, [students, debouncedSearchTerm] )

  // Calculate visible items based on scroll position
  const visibleItems = useMemo( () => {
    const startIndex = Math.floor( scrollTop / ITEM_HEIGHT )
    const endIndex = Math.min(
      startIndex + MAX_VISIBLE_ITEMS + 2, // Add buffer
      filteredStudents.length
    )

    return {
      startIndex,
      endIndex,
      items: filteredStudents.slice( startIndex, endIndex )
    }
  }, [filteredStudents, scrollTop] )

  const handleScroll = useCallback( ( e: React.UIEvent<HTMLDivElement> ) => {
    setScrollTop( e.currentTarget.scrollTop )
  }, [] )

  const handleStudentClick = useCallback( ( student: Student ) => {
    onStudentSelect( student )
    setSearchTerm( "" )
    setIsOpen( false )
  }, [onStudentSelect] )

  const handleOpenChange = useCallback( ( open: boolean ) => {
    setIsOpen( open )
    if ( open ) {
      // Focus search input when opening
      setTimeout( () => {
        searchInputRef.current?.focus()
      }, 100 )
    } else {
      setSearchTerm( "" )
      setScrollTop( 0 )
    }
  }, [] )

  // Close dropdown when clicking outside
  useEffect( () => {
    const handleClickOutside = ( event: MouseEvent ) => {
      const target = event.target as Element
      if ( !target.closest( "[data-student-select]" ) ) {
        setIsOpen( false )
      }
    }

    if ( isOpen ) {
      document.addEventListener( "mousedown", handleClickOutside )
      return () => document.removeEventListener( "mousedown", handleClickOutside )
    }
  }, [isOpen] )

  return (
    <div className="space-y-2" data-student-select>
      {/* Trigger Button */}
      <Button
        type="button"
        variant="outline"
        onClick={() => handleOpenChange( !isOpen )}
        className="hover:text-background w-full justify-between"
      >
        <span className="text-muted-foreground hover:text-background">
          {selectedStudents.length > 0 
            ? `${selectedStudents.length} students selected` 
            : "Select students"}
        </span>
        <ChevronDown className={cn( "size-4 transition-transform", isOpen && "rotate-180" )} />
      </Button>

      {/* Dropdown */}
      {isOpen && (
        <div className="bg-background relative z-50 w-full rounded-md border shadow-lg">
          {/* Search Input */}
          <div className="border-b p-2">
            <div className="relative">
              <Search className="focus-visible:border-primary focus:ring-primary focus-visible:ring-primary hover:text-background text-muted-foreground absolute left-2 top-2.5 size-4" />
              <Input
                ref={searchInputRef}
                placeholder={`Search ${students.length} students...`}
                value={searchTerm}
                onChange={( e ) => setSearchTerm( e.target.value )}
                className="focus-visible:border-primary focus:ring-primary focus-visible:ring-primary pl-8"
              />
            </div>
          </div>

          {/* Results Info */}
          <div className="text-muted-foreground border-b px-3 py-2 text-xs">
            {debouncedSearchTerm ? (
              <>
                Showing {filteredStudents.length} of {students.length} students
              </>
            ) : (
              <>Showing all {students.length} students</>
            )}
          </div>

          {/* Virtualized List */}
          <div className="relative">
            <div
              ref={scrollElementRef}
              className="overflow-auto"
              style={{ height: Math.min( CONTAINER_HEIGHT, filteredStudents.length * ITEM_HEIGHT ) }}
              onScroll={handleScroll}
            >
              {/* Virtual spacer for items above viewport */}
              <div style={{ height: visibleItems.startIndex * ITEM_HEIGHT }} />

              {/* Visible items */}
              <div>
                {visibleItems.items.map( ( student, index ) => {
                  const actualIndex = visibleItems.startIndex + index
                  const isSelected = selectedStudents.some( ( s ) => s.id === student.id )

                  return (
                    <div
                      key={student.id}
                      className={cn(
                        "hover:bg-accent flex cursor-pointer items-center px-3 py-2 transition-colors",
                        isSelected && "bg-accent text-accent-foreground"
                      )}
                      style={{ height: ITEM_HEIGHT }}
                      onClick={() => !isSelected && handleStudentClick( student )}
                    >
                      <div className="min-w-0 flex-1">
                        <div className="truncate font-medium">{student.label}</div>
                        <div className="text-muted-foreground truncate text-xs">{student.value}</div>
                      </div>
                      {isSelected && <div className="text-xs font-medium text-green-600">Selected</div>}
                    </div>
                  )
                })}
              </div>

              {/* Virtual spacer for items below viewport */}
              <div style={{ height: ( filteredStudents.length - visibleItems.endIndex ) * ITEM_HEIGHT }} />
            </div>

            {/* No results */}
            {filteredStudents.length === 0 && (
              <div className="text-muted-foreground p-4 text-center">
                No students found matching: {debouncedSearchTerm}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Selected Students Display */}
      {selectedStudents.length > 0 && (
        <div className="mt-2 flex flex-wrap gap-1">
          {selectedStudents.map( ( student ) => (
            <div
              key={student.id}
              className="bg-secondary text-secondary-foreground flex items-center gap-1 rounded-md px-2 py-1 text-sm"
            >
              <span className="text-background max-w-[200px] truncate">{student.label}</span>
              <button
                type="button"
                onClick={() => onStudentRemove( student.id )}
                className="text-secondary-foreground hover:text-destructive ml-1"
              >
                <X className="text-background size-3" />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

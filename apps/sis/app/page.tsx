"use client"
import { useState, useEffect, useMemo, Suspense } from "react"
import type { TermType } from "@/lib/data/types"
import { ONLINE_ACCEPTED_STATUSES } from "@/lib/data/admissions/online/constants"
import type { OnsiteApplicationForm } from "@/lib/data/admissions/onsite/types"
import type { OnlineAdmissionForm } from "@/lib/data/admissions/online/types"
import { useCurrentUser } from "@/app/actions/users/queries"
import { 
  useTerms, 
  useCurrentTerm 
} from "@/app/actions/terms/queries"
import { useApplications, useOnlineApplications } from "@/app/actions/admissions/queries"

import { NoNavWrapper } from "@/components/common/NoNavWrapper"
import { Warning } from "@/components/common/alerts/Warning"
import CurrentApplications from "@/components/dashboard/Current"
import Student from "@/components/dashboard/Student"
import Accepted from "@/components/dashboard/Accepted"
import Evaluations from "@/components/dashboard/Evaluations"

export default function Dashboard() {
  const { user, userLoading, userError, kindeUser: processedKindeUser } = useCurrentUser()
  const { applications, appLoading } = useApplications( user || processedKindeUser )
  const { onlineApplications, onlineAppLoading } = useOnlineApplications( user || processedKindeUser )
  const { currentTerm } = useCurrentTerm()
  const { terms } = useTerms()

  const [term, setTerm] = useState<TermType>()
  const [accepted, setAccepted] = useState<boolean>( false )
  const [studentType, setStudentType] = useState<string>( "onsite" )
  const [updateProfile, setUpdateProfile] = useState<boolean>( false )
  const [applicationId, setApplicationId] = useState<string>()
  const [application, setApplication] = useState<OnlineAdmissionForm | OnsiteApplicationForm>()

  useEffect( () => {
    if ( !currentTerm?.id || !terms?.length ) return

    // Check for onsite applications first (priority)
    const currentOnsiteApps = applications?.filter( ( app: OnsiteApplicationForm ) => Number( app.terms ) >= Number( currentTerm.id ) ) || []

    // Find accepted onsite application
    const acceptedOnsiteApp = currentOnsiteApps.find( ( app: OnsiteApplicationForm ) => app.status === "accepted" )

    if ( acceptedOnsiteApp ) { // Onsite application takes priority
      const acceptedTerm = terms.find( ( term: TermType ) => Number( term.id ) === Number( acceptedOnsiteApp.terms ) )
      setTerm( acceptedTerm )
      setAccepted( !!acceptedTerm )
      setApplication( acceptedOnsiteApp )
      setApplicationId( acceptedOnsiteApp._id )
      
      // Check if it's a specialized program
      if ( acceptedOnsiteApp.applicationInfo?.programApplyingFor === "University/Specialized Program" ) {
        setStudentType( "program" )
      }
      
      return // Exit early since onsite takes priority
    }
    
    // If no accepted onsite application, check online applications
    const currentOnlineApps = onlineApplications?.filter( ( app: OnlineAdmissionForm ) => Number( app.terms ) >= Number( currentTerm.id ) ) || []
    // Find accepted online application
    const acceptedOnlineApp = currentOnlineApps.find( ( app: OnlineAdmissionForm ) => ONLINE_ACCEPTED_STATUSES.includes( app.status ) || app.submission_date )

    if ( acceptedOnlineApp ) {
      const acceptedTerm = terms.find( ( term: TermType ) => Number( term.id ) === Number( acceptedOnlineApp.terms ) )

      setTerm( acceptedTerm )
      setAccepted( !!acceptedTerm )
      setApplication( acceptedOnlineApp )
      setApplicationId( acceptedOnlineApp._id )
      setStudentType( "online" )
    }
  }, [currentTerm, applications, onlineApplications, terms])

  useMemo( () => {
    if ( user?.profile?.display_name === "John Doe" || user?.profile?.first_name === "Student" ) {
      setUpdateProfile( true )
    }
  }, [user] )


  if ( userLoading ) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full size-8 border-b-2 border-primary mx-auto" />
          <p className="mt-2 text-sm text-gray-600">Loading the dashboard...</p>
        </div>
      </div>
    )
  }

  if ( userError ) {
    return <NoNavWrapper>An error occurred: {userError}</NoNavWrapper>
  }

  if ( !user || !user.profile ) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full size-8 border-b-2 border-primary mx-auto" />
          <p className="mt-2 text-sm text-gray-600">Loading profile...</p>
        </div>
      </div>
    )
  }

  const currentUser = user || processedKindeUser
  const userType = ( user?.profile && user.profile?.user_type ) 
    ? user.profile.user_type 
    : "student"

  return (
    <>
      <Suspense fallback={<div>Loading...</div>}>
        {updateProfile && (
          <div className="my-4">
            <Warning>
              {"Welcome to Qasid's Student Information System. Before submitting your application, please update your "}{" "}
              <a className="text-primary hover:underline" href="/profile">
                profile page.
              </a>
            </Warning>
          </div>
        )}

        {/* Remove student type after going live with online orientation */}
        {( userType === "student" || userType === "applicant" ) && (
          <>
            {accepted ? (
              <>
                {( studentType === "program" || studentType === "onsite" ) && (
                  <>
                    <Evaluations user={currentUser} />
                    <Accepted
                      id={applicationId}
                      user={currentUser}
                      term={term}
                      type={studentType}
                      application={application}
                    />
                  </>
                )}

                {studentType === "online" && (
                  <>
                    <CurrentApplications
                      terms={terms}
                      applications={applications}
                      onlineApplications={onlineApplications}
                      appLoading={appLoading}
                      onlineAppLoading={onlineAppLoading}
                    />
                  </>
                )}
              </>
            ) : (
              <>
                <CurrentApplications
                  terms={terms}
                  applications={applications}
                  onlineApplications={onlineApplications}
                  appLoading={appLoading}
                  onlineAppLoading={onlineAppLoading}
                />
              </>
            )}
          </>
        )}

        {userType === "teacher" && <Student />}

        {userType === "admin" && (
          <>
            {accepted ? (
              <Accepted
                id={applicationId}
                user={currentUser}
                term={{
                  "id": 20254,
                  "title": "2025 Fall",
                  "comments": "Orientation: September 2, 2025",
                  "open_for_apply": true,
                  "start_date": "2025-09-03T00:00:00.000Z",
                  "end_date": "2025-12-04T00:00:00.000Z",
                  "canvasid": ""
                }}
                type={"onsite"}
                application={application}
              />
            ) : (
              <CurrentApplications
                terms={terms}
                applications={applications}
                onlineApplications={onlineApplications}
                appLoading={appLoading}
                onlineAppLoading={onlineAppLoading}
              />
            )}
          </>
        )}
      </Suspense>
    </>
  )
}

"use client"

import type React from "react"
import { Suspense, useState, useEffect } from "react"
import dynamic from "next/dynamic"
import { Analytics } from "@vercel/analytics/react"
import { useKindeBrowserClient } from "@kinde-oss/kinde-auth-nextjs"
import { Loader2 } from "lucide-react"

import { SWRProvider } from "@/utils/swr-config"
import { UserProvider } from "@/utils/context/user-context"
import { ThemeProvider } from "@/utils/context/theme-provider"
import { AuthProvider } from "@/utils/auth/AuthProvider"
import { Container } from "@/components/common/Container"
import { ErrorBoundary } from "@/app/error-boundary"
import NoSSR from "@/app/no-ssr"

const App = dynamic( () => import( "@/components/common/_shell" ), {
  loading: () => (
    <Container>
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full size-8 border-b-2 border-primary mx-auto" />
          <p className="mt-2 text-sm text-gray-600">Loading...</p>
        </div>
      </div>
    </Container>
  )
})

interface ClientProvidersProps { children: React.ReactNode }

export default function ClientProviders({ children }: ClientProvidersProps ) {
  const { isAuthenticated, isLoading, user } = useKindeBrowserClient()
  const [shouldRedirect, setShouldRedirect] = useState( false )

  useEffect( () => {
    if ( !isLoading ) { // Only attempt redirect after Kinde has finished loading
      if ( !isAuthenticated ) {
        // Add a small delay to ensure Kinde has fully processed the authentication state
        const timer = setTimeout(() => {
          setShouldRedirect( true )
        }, 100 )
        return () => clearTimeout( timer )
      } else {
        setShouldRedirect( false ) // Reset redirect flag if user becomes authenticated
      }
    }
  }, [isLoading, isAuthenticated] )

  useEffect( () => {
    if ( shouldRedirect ) {
      window.location.href = `/api/auth/login?post_login_redirect_url=${encodeURIComponent( window.location.pathname )}`
    }
  }, [shouldRedirect] )

  // Show loading while Kinde is initializing
  if ( isLoading ) {
    return (
      <Container>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="mr-2 text-primary size-4 animate-spin" />
          <span>Loading...</span>
        </div>
      </Container>
    )
  }

  // Show loading while redirect is happening
  if ( !isAuthenticated ) {
    return (
      <Container>
        <div className="flex items-center justify-center min-h-screen">
          <Loader2 className="mr-2 text-primary size-4 animate-spin" />
          <span>Redirecting to login...</span>
        </div>
      </Container>
    )
  }

  return (
    <ErrorBoundary>
      <NoSSR
        fallback={
          <Container>
            <div className="flex items-center justify-center min-h-screen">
              <div className="text-center">
                <div className="animate-spin rounded-full size-8 border-b-2 border-primary mx-auto" />
                <p className="mt-2 text-sm text-gray-600">Loading...</p>
              </div>
            </div>
          </Container>
        }
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
          storageKey="qasid-theme"
        >
          <AuthProvider>
            <UserProvider>
              <SWRProvider>
                <Suspense
                  fallback={
                    <Container>
                      <div className="flex items-center justify-center min-h-screen">
                        <div className="text-center">
                          <div className="animate-spin rounded-full size-8 border-b-2 border-primary mx-auto" />
                          <p className="mt-2 text-sm text-gray-600">Loading...</p>
                        </div>
                      </div>
                    </Container>
                  }
                >
                  <App>{children}</App>
                </Suspense>
              </SWRProvider>
            </UserProvider>
          </AuthProvider>
          <Analytics />
        </ThemeProvider>
      </NoSSR>
    </ErrorBoundary>
  )
}

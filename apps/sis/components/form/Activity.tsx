"use client"

import { useState, use<PERSON><PERSON>back, useMemo } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { format } from "date-fns"
import dayjs from "dayjs"
import utc from "dayjs/plugin/utc"
import customParseFormat from "dayjs/plugin/customParseFormat"
import { CheckCircle, CalendarIcon } from "lucide-react"

import { Button } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { Textarea } from "@/app/components/ui/textarea"
import { Checkbox } from "@/app/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/app/components/ui/radio-group"
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/app/components/ui/form"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/app/components/ui/select"
import { 
  Collapsible, 
  CollapsibleContent, 
  CollapsibleTrigger 
} from "@/app/components/ui/collapsible"
import { Calendar } from "@/app/components/ui/calendar"
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from "@/app/components/ui/popover"
import { useClassroomCapacityStatus } from "@/hooks/useClassroomCapacityStatus"
import { cn } from "@/lib/utils"
import { VirtualizedStudentSelect } from "@/lib/virtualized-student-select"
import { TermType } from "@/lib/data/types"
import { ABBREVIATED_DAYS_OF_WEEK } from "@/lib/data/constants"
import {
  compilePrograms,
  compilePartners,
  compileStudents,
  compileTeachers,
  compileRooms,
  compileTerms,
} from "@/lib/data/parsers"
import { calendarTimes, calendarDuration } from "@/lib/data/timetable/constants"

import { useTerms } from "@/app/actions/terms/queries"
import { useRooms } from "@/app/actions/rooms/queries"
import { usePrograms } from "@/app/actions/programs/queries"
import { usePartners } from "@/app/actions/partners/queries"
import { useTeachers, useStudents } from "@/app/actions/users/queries"

dayjs.extend( utc )
dayjs.extend( customParseFormat )
dayjs.tz.setDefault( "UTC" )

const formSchema = z.object({
  id: z.any().optional(),
  termid: z
    .object({
      _id: z.number().or( z.string() ),
      id: z.string(),
      value: z.string(),
      label: z.string()
    })
    .optional(),
  term: z.any().optional(),
  course: z.string().min(1, { message: "Title is required" }),
  title: z.string().optional(),
  program: z
    .object({
      id: z.string().or( z.number() ).optional(),
      value: z.string().optional(),
      label: z.string().optional(),
      color: z.string().optional()
    })
    .optional()
    .nullable(),
  partner: z.any().optional(),
  classroom: z.any().optional(),
  instructor: z
    .object({
      id: z.string().or( z.number() ).optional(),
      value: z.string().optional(),
      label: z.string().optional()
    })
    .optional()
    .nullable(),
  students: z.array(
    z.object({
      id: z.string().or( z.number() ),
      value: z.string(),
      label: z.string()
    })
  ).optional(),
  weekdays: z.array(z.string()).optional(),
  times: z.any().optional(),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  start_time: z.object({
    id: z.string().or(z.number()).optional(),
    value: z.string(),
    label: z.string()
  }),
  duration: z.object({
    _id: z.string().or(z.number()).optional(),
    id: z.string().or(z.number()).optional(),
    value: z.string(),
    label: z.string()
  }),
  zoom: z.string().optional(),
  notes: z.string().optional(),
  canvas_status: z.string().optional(),
  status: z.string().optional()
})

type FormValues = z.infer<typeof formSchema>

type ActivityFormTypes = {
  term?: number | string | TermType
  section?: any
  roomCap?: any
  onSave?: any
  setOpen?: Function
  allAppointments?: any
}

export function ActivityForm({ term, section, roomCap, onSave, setOpen, allAppointments }: ActivityFormTypes ) {
  const { teachers } = useTeachers()
  const { students } = useStudents()
  const { programs } = usePrograms()
  const { partners } = usePartners()
  const { rooms } = useRooms()
  const { terms } = useTerms()

  const [expanded, setExpanded] = useState( false )
  const [selectedDays, setSelectedDays] = useState<string[]>( section?.times?.days || ABBREVIATED_DAYS_OF_WEEK )
  const [isSaving, setIsSaving] = useState<boolean>( false )
  const [error, setError] = useState<string | undefined>()
  const [message, setMessage] = useState<string | undefined>()

  const allTerms = useMemo( () => compileTerms( terms ), [terms] )
  const allPrograms = useMemo( () => compilePrograms( programs ), [programs] )
  const allStudents = useMemo( () => compileStudents( students ), [students] )
  const allTeachers = useMemo( () => compileTeachers( teachers ), [teachers] )
  const allRooms = useMemo( () => compileRooms( rooms ), [rooms, roomCap] )
  const allPartners = useMemo( () => compilePartners( partners, (term as any)?.id || term ), [partners, term] )
  const currentTerm = useMemo( () => {
    console.log("ActivityForm - term:", term, "allTerms:", allTerms)
    if ( typeof term === "object" && term?.id ) {
      return {
        _id: typeof term.id === "string" ? parseInt( term.id ) : term.id,
        id: String( term.id ),
        value: String( term.id ),
        label: term.title
      }
    } else {
      // Fix: Compare against trm.id or trm.value, not the object itself
      const foundTerm = allTerms.find( ( trm: any ) => String( trm.id ) === String( term ) || String( trm.value ) === String( term ) )
      return foundTerm
    }
  }, [term, allTerms] )

  const form = useForm<FormValues>({
    resolver: zodResolver( formSchema as any ),
    defaultValues: {
      ...section,
      termid: currentTerm,
      term: currentTerm,
      students: section?.students ?? [],
      weekdays: section?.weekdays ?? ABBREVIATED_DAYS_OF_WEEK,
      start_date: section?.start_date ?? format(new Date(), "yyyy-MM-dd"),
      end_date: section?.end_date ?? format(new Date(), "yyyy-MM-dd"),
      start_time: section?.start_time ?? { id: "09:00", value: "09:00", label: "09:00" },
      duration: section?.duration ?? { id: "30", value: "30", label: "30 minutes" },
      status: section?.status ?? "Active",
      canvas_status: section?.canvas_status ?? "pending"
    }
  })

  const selectedDaysWatch = form.watch( "weekdays" ) || []
  const selectedStart = form.watch( "start_time" )?.value || "09:00"
  const selectedDuration = form.watch( "duration" )?.value || "60"
  const editingId = section?.id ?? section?._id // or whatever identifies the current apt

  const roomsWithStatus = useClassroomCapacityStatus(
    allRooms,
    allAppointments,
    selectedDaysWatch,
    selectedStart,
    selectedDuration,
    editingId
  )

  const toggleDay = useCallback( ( day: string ) => {
    setSelectedDays( ( current ) => {
      const isSelected = current.includes(day)
      const newDays = isSelected ? current.filter((d) => d !== day) : [...current, day]
      form.setValue("weekdays", newDays)
      return newDays
    })
  }, [form] )

  const toggleAllDays = useCallback( () => {
    const newDays = selectedDays.length > 0 ? [] : ABBREVIATED_DAYS_OF_WEEK
    setSelectedDays(newDays)
    form.setValue("weekdays", newDays)
  }, [selectedDays, form] )

  const handleSubmitForm = useCallback( async ( values: FormValues ) => {
    if ( onSave ) {
      await onSave( values ) // 🔑 parent handles DB + state
      if ( setOpen ) setOpen( false )
    }
  }, [onSave, setOpen] )

  if ( !allTeachers.length || !allRooms.length || !allPrograms.length || !currentTerm ) {
    console.log("ActivityForm - Loading state:", {
      allTeachers: allTeachers.length,
      allRooms: allRooms.length,
      allPrograms: allPrograms.length,
      currentTerm,
      term
    })
    return (
      <Form {...form}>
        <div className="p-4">Loading form data...</div>
      </Form>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmitForm)} className="space-y-6">
        <div className="pb-4">
          <p className="mt-1 text-sm leading-6 text-muted-foreground">
            Make sure to fill out all fields.
          </p>

          <div className="mt-6 w-full">
            <div className="grid grid-cols-2 gap-x-2">
              <FormField
                control={form.control}
                name="course"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>CLASS NAME</FormLabel>
                    <FormControl>
                      <Input 
                        className="focus-visible:border-primary focus:ring-primary focus-visible:ring-primary"
                        placeholder="Enter a title..." 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="termid"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SELECT TERM</FormLabel>
                    <Select
                      defaultValue={String(term) || "20253"}
                      onValueChange={( value ) => {
                        const selectedTerm = allTerms.find( ( t ) => t.id === value )
                        field.onChange( selectedTerm )
                      }}
                      value={field.value?.id}
                    >
                      <FormControl>
                        <SelectTrigger className="focus-visible:border-primary focus:ring-primary focus-visible:ring-primary">
                          <SelectValue placeholder="Select term" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {allTerms.map( ( term ) => (
                          <SelectItem key={term.id} value={term.id}>
                            {term.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-x-2 my-4">
              <FormField
                control={form.control}
                name="program"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SELECT PROGRAM</FormLabel>
                    <Select
                      onValueChange={( value ) => {
                        const selectedProgram = allPrograms.find( ( p ) => p.id === value )
                        if (selectedProgram) {
                          // include color when saving into form state
                          field.onChange({
                            id: selectedProgram.id,
                            value: selectedProgram.value,
                            label: selectedProgram.label,
                            color: selectedProgram.color
                          })
                        }
                      }}
                      defaultValue={""}
                      value={field.value?.id as any}
                    >
                      <FormControl>
                        <SelectTrigger className="focus-visible:border-primary focus:ring-primary focus-visible:ring-primary">
                          <SelectValue placeholder="Select program" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {allPrograms.map( ( program ) => (
                          <SelectItem key={program.id} value={program.id}>
                            {program.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="classroom"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SELECT CLASSROOM</FormLabel>
                    <Select
                      onValueChange={( value ) => {
                        const selectedRoom = allRooms.find( ( r ) => r.id === value )
                        field.onChange( selectedRoom )
                      }}
                      value={field.value?.id}
                      defaultValue={""}
                    >
                      <FormControl>
                        <SelectTrigger className="focus-visible:border-primary focus:ring-primary focus-visible:ring-primary">
                          <SelectValue placeholder="Select classroom" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {/* {allRooms.map( ( room ) => (
                          <SelectItem key={room.id} value={room.id}>
                            {room.label}
                          </SelectItem>
                        ))} */}
                        {roomsWithStatus.map(room => (
                          <SelectItem
                            key={room.id}
                            value={room.id}
                            disabled={room.isFull}
                            className={room.isFull ? "text-red-500" : ""}
                          >
                            {room.label}
                            {room.isFull && <span className="ml-2 text-xs text-red-500">(At capacity)</span>}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-x-2 my-4">
              <FormField
                control={form.control}
                name="instructor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SELECT TEACHER</FormLabel>
                    <Select
                      onValueChange={( value ) => {
                        const selectedTeacher = allTeachers.find( ( t ) => t.id === value )
                        field.onChange( selectedTeacher )
                      }}
                      defaultValue={""}
                      value={field.value?.id as any}
                    >
                      <FormControl>
                        <SelectTrigger className="focus-visible:border-primary focus:ring-primary focus-visible:ring-primary">
                          <SelectValue placeholder="Select teacher/instructor" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {allTeachers.map( ( teacher ) => (
                          <SelectItem key={teacher.id} value={teacher.id}>
                            {teacher.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="students"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SELECT STUDENTS</FormLabel>
                    <VirtualizedStudentSelect
                      students={allStudents}
                      selectedStudents={field.value ?? []}
                      onStudentSelect={( student ) => {
                        const currentStudents = [...(field.value ?? [])]
                        const exists = currentStudents.some( ( s ) => s.id === student.id )
                        if (!exists) {
                          field.onChange( [...currentStudents, student] )
                        }
                      }}
                      onStudentRemove={( studentId ) => {
                        field.onChange((field.value ?? []).filter( ( s ) => s.id !== studentId ) )
                      }}
                    />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-x-2 my-4">
              <FormField
                control={form.control}
                name="start_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Start Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn( "w-full pl-3 text-left font-normal", !field.value && "text-muted-foreground" )}
                          >
                            {field.value 
                              ? dayjs( field.value ).format( "MMMM D, YYYY" ) 
                              : <span>Pick a date</span>}
                            <CalendarIcon className="ml-auto size-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value ? new Date( field.value ) : undefined}
                          onSelect={( date ) => field.onChange( date ? dayjs( date ).format( "YYYY-MM-DD" ) : "" )}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="end_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>End Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn( "w-full pl-3 text-left font-normal", !field.value && "text-muted-foreground" )}
                          >
                            {field.value ? dayjs( field.value ).format( "MMMM D, YYYY" ) : <span>Pick a date</span>}
                            <CalendarIcon className="ml-auto size-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value ? new Date( field.value ) : undefined}
                          onSelect={( date ) => field.onChange( date ? dayjs( date ).format( "YYYY-MM-DD" ) : "" )}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-x-2 my-4">
              <FormField
                control={form.control}
                name="start_time"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SELECT START TIME</FormLabel>
                    <Select
                      defaultValue={""}
                      onValueChange={( value ) => {
                        const selectedTime = calendarTimes.find( ( t ) => t.value === value )
                        field.onChange( selectedTime )
                      }}
                      value={field.value?.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select start time" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {calendarTimes.map( ( time ) => {
                          const [h, m] = time.value.split( ":" ).map( Number )
                          const hour12 = h % 12 === 0 ? 12 : h % 12
                          const ampm = h < 12 ? "AM" : "PM"
                          const label12 = `${hour12}:${m.toString().padStart( 2, "0" )} ${ampm}`
                          return (
                            <SelectItem key={time.value} value={time.value}>
                              {label12}
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>SELECT DURATION</FormLabel>
                    <Select
                      defaultValue={""}
                      onValueChange={( value ) => {
                        const selectedDuration = calendarDuration.find( ( d ) => d.value === value )
                        field.onChange( selectedDuration )
                      }}
                      value={field.value?.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select duration" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {calendarDuration.map( ( duration ) => (
                          <SelectItem key={duration.value} value={duration.value}>
                            {duration.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="col-span-full mt-8">
              <Collapsible open={expanded} onOpenChange={setExpanded} className="w-full">
                <CollapsibleTrigger asChild>
                  <Button variant="default" className="w-full flex justify-between">
                    <span>Click for more options...</span>
                    <svg
                      className={cn( "size-5", expanded ? "" : "rotate-180" )}
                      fill="none"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path d="M19 9l-7 7-7-7"></path>
                    </svg>
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="mt-6">
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-x-2 my-4">
                      <FormField
                        control={form.control}
                        name="partner"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>SELECT PARTNER</FormLabel>
                            <Select
                              defaultValue={""}
                              onValueChange={( value ) => {
                                const selectedPartner = allPartners.find( ( p ) => p.id === value )
                                field.onChange( selectedPartner )
                              }}
                              value={field.value?.id}
                            >
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select partner" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {allPartners.map( ( partner ) => (
                                  <SelectItem key={partner.id} value={partner.id}>
                                    {partner.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="zoom"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>ENTER ZOOM LINK</FormLabel>
                            <FormControl>
                              <Input 
                                className="focus-visible:border-primary focus:ring-primary focus-visible:ring-primary"
                                placeholder="Enter a zoom link (optional)" 
                                {...field} 
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="notes"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>ENTER NOTES (optional)</FormLabel>
                          <FormControl>
                            <Textarea 
                              placeholder="Enter notes here..." 
                              className="min-h-[100px] focus-visible:border-primary focus:ring-primary focus-visible:ring-primary" 
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="pb-4">
                      <h2 className="text-base font-semibold leading-6 text-secondary-foreground">Class Information</h2>
                      <p className="mt-1 text-sm leading-4 text-muted-foreground">
                        All information is required.
                      </p>

                      <div className="mt-2 grid grid-cols-1 gap-x-6 sm:gap-x-4 gap-y-8 sm:grid-cols-2">
                        <div className="mt-2">
                          <FormField
                            control={form.control}
                            name="weekdays"
                            render={() => (
                              <FormItem>
                                <div className="mb-4">
                                  <FormLabel>DAYS</FormLabel>
                                </div>
                                <div className="flex flex-wrap gap-2">
                                  {ABBREVIATED_DAYS_OF_WEEK.map( ( day ) => (
                                    <FormItem key={day} className="flex flex-row items-start space-x-3 space-y-0">
                                      <FormControl>
                                        <Checkbox
                                          checked={selectedDays.includes( day )}
                                          onCheckedChange={() => toggleDay( day )}
                                        />
                                      </FormControl>
                                      <FormLabel className="font-normal">{day}</FormLabel>
                                    </FormItem>
                                  ))}
                                </div>
                                <div 
                                  className="mt-4 flex items-center cursor-pointer" 
                                  onClick={toggleAllDays}
                                >
                                  <CheckCircle className="size-5 mr-2" />
                                  <span>Select / Deselect</span>
                                </div>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="mt-2">
                          <FormField
                            control={form.control}
                            name="status"
                            render={({ field }) => (
                              <FormItem className="space-y-3">
                                <FormLabel>ADVANCED OPTIONS</FormLabel>
                                <FormDescription>
                                  {"If the section is archived, it will be saved, but won't affect the timetable"}
                                </FormDescription>
                                <FormControl>
                                  <RadioGroup
                                    onValueChange={field.onChange}
                                    defaultValue={field.value}
                                    className="flex flex-col space-y-1"
                                  >
                                    <FormItem className="flex items-center space-x-3 space-y-0">
                                      <FormControl>
                                        <RadioGroupItem value="Active" />
                                      </FormControl>
                                      <FormLabel className="font-normal">Active</FormLabel>
                                    </FormItem>
                                    <FormItem className="flex items-center space-x-3 space-y-0">
                                      <FormControl>
                                        <RadioGroupItem value="Archived" />
                                      </FormControl>
                                      <FormLabel className="font-normal">Archived</FormLabel>
                                    </FormItem>
                                  </RadioGroup>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </div>
          </div>
        </div>

        {message && (
          <div className="bg-green-50 border border-green-200 text-green-800 rounded-md p-4">
            <div className="flex">
              <div className="shrink-0">
                <CheckCircle className="size-5 text-green-400" aria-hidden="true" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium">{message}</p>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4">
            <div className="flex">
              <div className="shrink-0">
                <svg
                  className="size-5 text-red-400"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                  aria-hidden="true"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium">{error}</p>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-end gap-x-2">
          <Button type="submit" disabled={isSaving}>
            {isSaving ? "Submitting..." : "Submit & Save"}
          </Button>
        </div>
      </form>
    </Form>
  )
}

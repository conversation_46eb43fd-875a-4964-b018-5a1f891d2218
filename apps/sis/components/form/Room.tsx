"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"

import { postRoom } from "@/app/actions/rooms/queries"
import { postLog } from "@/app/actions/timetable/queries"
import { useCurrentUser } from "@/app/actions/users/queries"

import { Navigation } from "@/components/common/Navigation"
import { Error } from "@/components/common/alerts/Error"
import { Success } from "@/components/common/alerts/Success"
import { Button } from "@/components/common/Button"

import { ChromePicker } from "react-color"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/app/components/ui/form"
import { Input } from "@/app/components/ui/input"

// Define the form schema with Zod
const roomFormSchema = z.object({
  id: z.string().optional(),
  name: z.string()
    .min(1, "Room name is required")
    .max(100, "Room name must be less than 100 characters")
    .trim(),
  amount: z.number()
    .min(1, "Number of students is required")
    .refine((val) => {
      const num = parseInt(val, 10)
      return !isNaN(num) && num > 0 && num <= 1000
    }, "Must be a valid number between 1 and 1000"),
  color: z.string()
    .min(1, "Color is required")
    .regex(/^#[0-9A-F]{6}$/i, "Must be a valid hex color")
})

type RoomFormValues = z.infer<typeof roomFormSchema>

interface RoomFormProps {
  room: {
    id?: string
    _id?: string
    value?: string
    name?: string
    amount?: number
    color?: string
  } | null
  setSelectedRoom: (room: any) => void
  setOpen: (open: boolean) => void
  mutate?: () => void
}

export const RoomForm = ({ room, setSelectedRoom, setOpen, mutate }: RoomFormProps) => {
  const { user } = useCurrentUser()
  const [isSaving, setIsSaving] = useState<boolean>( false )
  const [message, setMessage] = useState<string>( "" )
  const [error, setError] = useState<string>( "" )

  const form = useForm<RoomFormValues>({
    resolver: zodResolver(roomFormSchema),
    defaultValues: {
      id: room?.id ?? room?._id ?? "",
      name: room?.value ?? room?.name ?? "",
      amount: room?.amount ?? 0,
      color: room?.color ?? "#000000"
    },
    mode: "onChange"
  })

  const onSubmit = async (values: RoomFormValues) => {
    setIsSaving(true)
    setError("")
    setMessage("")

    try {
      const result = await postRoom(values)
      await postLog(user, "Edited classroom: " + values.name)

      if (result && (result.matchedCount || result.modifiedCount)) {
        setMessage("Changes to classroom saved, you can close this window now")
        setSelectedRoom(values)
        if (mutate) mutate()
      } else {
        setError("There was an error saving these classroom changes. Please try again.")
      }
    } catch (e) {
      console.error("Room form submission error:", e)
      setError("Something went wrong. Please contact the admin if this problem persists.")
    }

    setIsSaving(false)
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="pb-4">
          <p className="mt-1 text-sm leading-6 text-muted-foreground">
            All fields required.
          </p>

          <div className="mt-6 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
            <div className="sm:col-span-full">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium leading-6 text-muted-foreground">
                      ROOM NAME
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter a name..."
                        {...field}
                        className="block w-full rounded-md py-1.5 px-2.5 placeholder:text-muted-foreground focus:outline-none focus:ring-0 focus:border-primary focus:border-2 sm:text-sm sm:leading-6"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="sm:col-span-4">
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium leading-6 text-muted-foreground">
                      NUMBER OF STUDENTS ALLOWED
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter a number..."
                        min="1"
                        max="1000"
                        {...field}
                        className="block w-full rounded-md py-1.5 px-2.5 placeholder:text-muted-foreground focus:outline-none focus:ring-0 focus:border-primary focus:border-2 sm:text-sm sm:leading-6"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="col-span-full">
              <FormField
                control={form.control}
                name="color"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium leading-6 text-muted-foreground">
                      ROOM COLOR
                    </FormLabel>
                    <FormControl>
                      <div className="mt-2">
                        <ChromePicker
                          color={field.value}
                          onChangeComplete={(color: any) => field.onChange(color.hex)}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </div>

        {message && ( <Success message={message} /> )}
        {error && ( <Error title={error} /> )}

        <div className="flex justify-end gap-x-2">
          {!message ? (
            <Navigation
              cancel={() => setOpen( false )}
              saving={isSaving}
              getClickedButton={( _val: any ) => {}}
              submit={form.handleSubmit(onSubmit)}
              noSave={true}
            />
          ) : (
            <Button
              id="close_room_window"
              legend={true}
              onClick={() => setOpen( false )}
              text="Close"
            />
          )}
        </div>
      </form>
    </Form>
  )
}
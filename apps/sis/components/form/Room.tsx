import { useState } from "react"

import { postRoom } from "@/app/actions/rooms/queries"
import { postLog } from "@/app/actions/timetable/queries"
import { useCurrentUser } from "@/app/actions/users/queries"

import { Navigation } from "@/components/common/Navigation"
import { Error } from "@/components/common/alerts/Error"
import { Success } from "@/components/common/alerts/Success"
import { Button } from "@/components/common/Button"
import { InputField } from "@/components/timetable/common/form/Field"

import { ChromePicker } from "react-color"
import { useFormik } from "formik"
import * as yup from "yup"

export const RoomForm = ({ room, setSelectedRoom, setOpen, mutate }) => {
  const { user } = useCurrentUser()
  const [clickedButton, setClickedButton] = useState<boolean>( false )
  const [isSaving, setIsSaving] = useState<boolean>( false )
  const [message, setMessage] = useState<string>( "" )
  const [error, setError] = useState<string>( "" )

  const formik = useFormik({
    initialValues: {
      id: room.id ?? room._id ?? "",
      name: room.value ?? room.name ?? "",
      amount: room.amount ?? "",
      color: room.color ?? ""
    },
    validationSchema: yup.object({
      name: yup
        .string()
        .typeError( "Title is not valid" )
        .required( "Name is required" ),
      amount: yup
        .string()
        .typeError( "The number of students is required" )
        .required( "A number is required" ),
      color: yup
        .string()
        .typeError( "Room color is required" )
        .required( "Color is required" )
    }),
    enableReinitialize: true,
    onSubmit: async ( values ) => {
      setIsSaving( true )
      setClickedButton( false )

      try {    
        console.log("VALUES: ", values)    
        const result = await postRoom( values )
        await postLog( user, "Edited classroom: " + values.name )

        if ( result && ( result.matchedCount || result.modifiedCount ) ) {
          setMessage( "Changes to classroom saved, you can close this window now" )
          setSelectedRoom( values )
          if ( mutate ) mutate()
        } else setError( "There was an error saving these classroom changes" )
      } catch ( e ) {
        setError( "Something went wrong. Contact the admin." )
      }

      setIsSaving( false )
    }
  })

  console.log("FORMIK: ", formik)

  return (
    <form onSubmit={formik.handleSubmit} className="space-y-6">
      <div className="pb-4">
        <p className="mt-1 text-sm leading-6 text-muted-foreground">
          All fields required.
        </p>

        <div className="mt-6 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
          <div className="sm:col-span-full">
            <InputField 
              type="text"
              id="name"
              name="name"
              label="ROOM NAME"
              placeholder="Enter a name..."
              values={formik.values.name}
              errors={formik.errors.name}
              touched={formik.touched.name}
              handleBlur={formik.handleBlur}
              handleChange={formik.handleChange}
            />
          </div>

          <div className="sm:col-span-4">
            <InputField 
              type="number"
              id="amount"
              name="amount"
              label="NUMBER OF STUDENTS ALLOWED"
              placeholder="Enter a number..."
              values={formik.values.amount}
              errors={formik.errors.amount}
              touched={formik.touched.amount}
              handleBlur={formik.handleBlur}
              handleChange={formik.handleChange}
            />
          </div>

          <div className="col-span-full">
            <ChromePicker
              color={formik.values.color}
              onChangeComplete={( e ) => formik.setFieldValue( "color", e.hex, true )}
            />
          </div>
        </div>
      </div>

      {message && ( <Success message={message} /> )}
      {error && ( <Error title={error} /> )}

      <div className="flex justify-end gap-x-2">
        {!message ? (
          <Navigation 
            cancel={() => setOpen( false )}
            saving={isSaving} 
            getClickedButton={( val ) => setClickedButton( val )} 
            submit={formik.handleSubmit}
            noSave={true}
          />
        ) : (
          <Button
            id="close_room_window"
            legend={true}
            onClick={() => setOpen( false )}
            text="Close"
          />
        )}
      </div>
    </form>
  )
}
"use client"

import type React from "react"
import { useState, useEffect, useMemo, useRef } from "react"
import { DndProvider, useDrag, useDrop } from "react-dnd"
import { HTML5Backend } from "react-dnd-html5-backend"
import { ABBREVIATED_DAYS_OF_WEEK } from "@/lib/data/constants"
import {
  dbToCanonical,
  instructorsToResources,
  roomsToResources,
  flattenForForm,
  SchedulerCanonical,
  InstructorDropDown,
  RoomDropDown,
  isEmptyObject
} from "@/lib/data/timetable/data-converter"

import { AddUserForm } from "@/components/form/User"
import { ActivityDialog } from "@/components/timetable/common/ui/Dialog"
import { ActivityForm } from "@/components/form/Activity"
import "@/styles/scheduler.css"

interface TimeSlot {
  time: string
  display: string
}

interface CustomSchedulerProps {
  mode: "teacher" | "classroom"
  term: any
  instructors?: InstructorDropDown[]
  rooms?: RoomDropDown[]
  appointments: SchedulerCanonical[]
  setAppointments: any
  unassignedAppointments: SchedulerCanonical[]
  setUnassignedAppointments: any
  appointmentMap?: Record<string, any>
  onAppointmentUpdate?: ( apt: SchedulerCanonical ) => void
  onAppointmentCreate?: ( apt: SchedulerCanonical ) => void
  onAppointmentDelete?: ( id: string ) => void
  onFormUpdate?: ( apt: SchedulerCanonical ) => void
  onFormCreate?: ( apt: SchedulerCanonical ) => void
}

const Scheduler = ({
  mode,
  term,
  appointments = [],
  setAppointments,
  unassignedAppointments = [],
  setUnassignedAppointments,
  instructors = [],
  rooms = [],
  appointmentMap,
  onAppointmentUpdate,
  onAppointmentCreate,
  onAppointmentDelete,
  onFormUpdate,
  onFormCreate
}: CustomSchedulerProps ) => {
  const appointment = useRef<any>( "" )
  const tableRef = useRef<HTMLDivElement>( null )
  const menuRef = useRef<HTMLDivElement>( null )
  const scrollProxyRef = useRef<HTMLDivElement>( null )
  const [contextMenu, setContextMenu] = useState<{
    show: boolean
    x: number
    y: number
    type: "appointment" | "cell" | "resource" | "classroom" | "teacher"
    data?: any
  }>({ show: false, x: 0, y: 0, type: "cell", data: undefined })
  const [focusedAppointments, setFocusedAppointments] = useState<{
    [key: string]: string // key = `${resourceId}-${timeSlot}`, value = appointment.id
  }>({})
  const [menuPosition, setMenuPosition] = useState({ left: contextMenu.x, top: contextMenu.y })
  const [resourceType, setResourceType] = useState<"teacher" | "classroom">( mode )
  const [resources, setResources] = useState<any[]>([])
  const [hoveredId, setHoveredId] = useState<string | null>( null )
  const [dialog, setDialog] = useState<string>( "" )
  const [open, setOpen] = useState<boolean>( false )

  useEffect( () => {
    if ( mode === "teacher" ) {
      setResources( instructorsToResources( instructors ) )
      setResourceType( "teacher" )
    }
  }, [instructors, resources.length, mode] )

  useEffect( () => {
    if ( mode === "classroom" ) {
      setResources( roomsToResources( rooms ) )
      setResourceType( mode )
    }
  }, [rooms, resources.length, mode] )

  useEffect( () => {
    if ( contextMenu.show && menuRef.current ) {
      const menuWidth = menuRef.current.offsetWidth
      const menuHeight = menuRef.current.offsetHeight
      const viewportWidth = window.innerWidth
      const viewportHeight = window.innerHeight
      let left = contextMenu.x
      let top = contextMenu.y
  
      // Adjust horizontally if overflowing
      if ( left + menuWidth > viewportWidth ) {
        left = Math.max( 0, viewportWidth - menuWidth - 4 ) // 4px padding
      }
      // Adjust vertically if overflowing (show above cursor)
      if ( top + menuHeight > viewportHeight ) {
        top = Math.max( 0, top - menuHeight )
      }
      setMenuPosition({ left, top })
    }
  }, [contextMenu] )

  const timeSlots: TimeSlot[] = useMemo( () => {
    const slots: TimeSlot[] = []
    for ( let hour = 8; hour < 22; hour++ ) {
      const hour12 = hour % 12 === 0 ? 12 : hour % 12
      const ampm = hour < 12 ? "AM" : "PM"
      slots.push({ time: `${hour.toString().padStart(2, "0")}:00`, display: `${hour12}:00 ${ampm}` })
      slots.push({ time: `${hour.toString().padStart(2, "0")}:30`, display: `${hour12}:30 ${ampm}` })
    }
    return slots
  }, [] )

  const calculateTimeSlotSpan = ( startTime: string, endTime: string ): number => {
    const startIndex = timeSlots.findIndex( ( slot ) => slot.time === startTime )
    const endIndex = timeSlots.findIndex( ( slot ) => slot.time === endTime )
    if ( startIndex === -1 || endIndex === -1 ) return 1
    return Math.max( 1, endIndex - startIndex )
  }





  const handleGlobalContextMenu = useMemo( () => ( e: MouseEvent ) => {
    const target = e.target as Element
    const schedulerContainer = document.getElementById( "qasid-scheduler" )

    if ( schedulerContainer && schedulerContainer.contains( target ) ) {
      e.preventDefault()
      e.stopPropagation()

      const x = e.clientX
      const y = e.clientY
      let clickedElement: any = target
      let appointmentId: any = null
      let resourceId: any = null
      let timeSlot: any = null

      while ( clickedElement && clickedElement !== schedulerContainer ) {
        const element = clickedElement as HTMLElement

        if ( element.dataset.appointmentId ) {
          appointmentId = element.dataset.appointmentId
          break
        }

        if ( element.dataset.resourceId && element.dataset.timeSlot ) {
          resourceId = element.dataset.resourceId
          timeSlot = element.dataset.timeSlot
          break
        }

        if (element.dataset.resourceId && !element.dataset.timeSlot) {
          const resourceId = element.dataset.resourceId
  
          const menuWidth = 150
          const menuHeight = 150
          const viewportWidth = window.innerWidth
          const viewportHeight = window.innerHeight
      
          let finalX = x
          let finalY = y
      
          if ( x + menuWidth > viewportWidth ) {
            finalX = x - menuWidth
          }
      
          if ( y + menuHeight > viewportHeight ) {
            finalY = y - menuHeight
          }
        
          setContextMenu({
            show: true,
            x: finalX,
            y: finalY,
            type: "resource",
            data: { resourceId }
          })
          return
        }

        clickedElement = clickedElement.parentElement
      }

      if ( appointmentId ) {
        const appointment = appointments.find( ( apt ) => apt.id === appointmentId )
        if ( appointment ) {
          const menuWidth = 150
          const menuHeight = 150
          const viewportWidth = window.innerWidth
          const viewportHeight = window.innerHeight
      
          let finalX = x
          let finalY = y
      
          if ( x + menuWidth > viewportWidth ) {
            finalX = x - menuWidth
          }
      
          if ( y + menuHeight > viewportHeight ) {
            finalY = y - menuHeight
          }
      
          setContextMenu({
            show: true,
            x: finalX,
            y: finalY,
            type: "appointment",
            data: appointment
          })
        }
      } else if ( resourceId && timeSlot ) {
        const menuWidth = 150
        const menuHeight = 90
        const viewportWidth = window.innerWidth
        const viewportHeight = window.innerHeight
    
        let finalX = x
        let finalY = y
    
        if ( x + menuWidth > viewportWidth ) {
          finalX = x - menuWidth
        }
    
        if ( y + menuHeight > viewportHeight ) {
          finalY = y - menuHeight
        }
    
        setContextMenu({
          show: true,
          x: finalX,
          y: finalY,
          type: "cell",
          data: { resourceId, timeSlot }
        })
      }
    }
  }, [appointments] )

  const handleGlobalClick = useMemo( () => () => {
    setContextMenu({ show: false, x: 0, y: 0, type: "cell" })
  }, [] )

  useEffect( () => {
    const tableEl = tableRef.current
    const proxyEl = scrollProxyRef.current
  
    if ( !tableEl || !proxyEl ) return
  
    const syncTableToProxy = () => {
      proxyEl.scrollLeft = tableEl.scrollLeft
    }
    const syncProxyToTable = () => {
      tableEl.scrollLeft = proxyEl.scrollLeft
    }
  
    tableEl.addEventListener( "scroll", syncTableToProxy )
    proxyEl.addEventListener( "scroll", syncProxyToTable )
  
    return () => {
      tableEl.removeEventListener( "scroll", syncTableToProxy )
      proxyEl.removeEventListener( "scroll", syncProxyToTable )
    }
  }, [] )

  useEffect( () => {
    const handleKey = ( e: KeyboardEvent ) => {
      if ( e.key === "Escape" ) {
        setContextMenu({ show: false, x: 0, y: 0, type: "cell" })
      }
    }
    document.addEventListener( "keydown", handleKey )
    return () => document.removeEventListener( "keydown", handleKey )
  }, [] )

  useEffect( () => {
    const handleScroll = () => setContextMenu({ show: false, x: 0, y: 0, type: "cell" })

    document.addEventListener( "scroll", handleScroll, true )
    document.addEventListener( "contextmenu", handleGlobalContextMenu )
    document.addEventListener( "click", handleGlobalClick )

    return () => {
      document.removeEventListener( "scroll", handleScroll, true )
      document.removeEventListener( "contextmenu", handleGlobalContextMenu )
      document.removeEventListener( "click", handleGlobalClick )
    }
  }, [handleGlobalContextMenu, handleGlobalClick] )

  const addMinutes = ( time: string, minutes: number ): string => {
    const [hours, mins] = time.split( ":" ).map( Number )
    const totalMinutes = hours * 60 + mins + minutes
    const newHours = Math.floor( totalMinutes / 60 )
    const newMins = totalMinutes % 60
    return `${newHours.toString().padStart( 2, "0" )}:${newMins.toString().padStart( 2, "0" )}`
  }

  const handleContextMenuAction = ( action: string ) => {
    const { type, data } = contextMenu
    switch ( action ) {
      case "edit":
        const latest = appointmentMap ? appointmentMap[data.id] : null
        if ( latest ) {
          appointment.current = flattenForForm( dbToCanonical( latest ), rooms )
        } else {
          appointment.current = flattenForForm( data, rooms )
        }
        setDialog( "course" )
        setOpen( true )
        break
      case "delete":
        if ( type === "appointment" ) {
          setAppointments( ( prev ) => prev.filter( ( apt ) => apt.id !== data.id ) )
          onAppointmentDelete?.( data.id )
        }
        break
      case "create":
        if ( type === "cell" ) {
          const newAppointment: SchedulerCanonical = {
            id: `apt-${Date.now()}`,
            termid: parseInt( term?.id ),
            title: "New Class",
            course: "New Course",
            instructor: instructors.find( i => i.id === data.resourceId ) ?? {},
            classroom: rooms.find( i => i.id === data.resourceId ) ?? "",
            times: {
              days: [],
              start_time: data.timeSlot,
              end_time: addMinutes( data.timeSlot, 60 ),
              duration: "60"
            },
            students: [],
            student_count: 0,
            program: {},
            notes: "",
            status: "ready"
          }
          onAppointmentCreate?.( newAppointment )
        }
        break
      case "unassign":
        if ( type === "appointment" ) {
          const unassignedApt: SchedulerCanonical = {
            ...data,
            instructor: {},
            times: { ...data.times, start_time: "", end_time: "" }
          }
          setUnassignedAppointments( ( prev ) => [...prev, unassignedApt] )
          setAppointments( ( prev ) => prev.filter( ( apt ) => apt.id !== data.id ) )
          onAppointmentUpdate?.( unassignedApt )
        }
        break
      case "addResource":
        if ( mode === "classroom" ) {
          console.log("ADD CLASSROOM")
        }
        if ( mode === "teacher" ) {
          console.log("ADD TEACHER")
          setDialog( "teacher" )
          setOpen( true )
        }
        break
      case "editResource":
        if ( mode === "classroom" ) {
          console.log("EDIT CLASSROOM")
        }
        if ( mode === "teacher" ) {
          console.log("EDIT TEACHER")
          setDialog( "teacher" )
          setOpen( true )
        }
        break
      case "deleteResource":
        if ( dialog === "classroom" ) {
          console.log("DELETE CLASSROOM")
        }
        if ( dialog === "teacher" ) {
          console.log("DELETE TEACHER")
        }
        break
      case "duplicate":
        if ( type === "appointment" ) {
          const duplicatedApt: SchedulerCanonical = {
            ...data,
            id: `apt-${Date.now()}`,
            instructor: {},
            classroom: "",
            times: { ...data.times, start_time: "", end_time: "" }
          }
          setUnassignedAppointments( ( prev ) => [...prev, duplicatedApt] )
          onAppointmentCreate?.( duplicatedApt )
        }
        break
    }
    setContextMenu({ show: false, x: 0, y: 0, type: "cell" })
  }

  const handleCloseContextMenu = ( e: React.MouseEvent ) => {
    e.stopPropagation()
    setContextMenu({ show: false, x: 0, y: 0, type: "cell" })
  }

  const handleCycleFocus = (cellKey: string, cellAppointments: SchedulerCanonical[]) => {
    if (cellAppointments.length <= 1) return

    setFocusedAppointments((prev) => {
      const currentId = prev[cellKey]
      const currentIdx = cellAppointments.findIndex(a => a.id === currentId)
      const nextIdx = (currentIdx + 1) % cellAppointments.length
      const nextId = cellAppointments[nextIdx]?.id || cellAppointments[0].id

      return {
        ...prev,
        [cellKey]: nextId
      }
    })
  }


  const DraggableAppointment = ({ 
    appointment,
    isUnassigned = false,
    timeSlotSpan = 1,
    isOverlapping,
    zIndex = 10,
    isFocused = false,
    onClick,
    onContextMenu
  }: {
    appointment: SchedulerCanonical
    isUnassigned?: boolean
    timeSlotSpan?: number
    isOverlapping?: boolean
    zIndex?: number
    isFocused?: boolean
    onClick?: React.MouseEventHandler<HTMLDivElement>
    onContextMenu?: any
  }) => {
    const [{ isDragging }, drag] = useDrag({
      type: "appointment",
      item: { ...appointment, isUnassigned },
      collect: ( monitor ) => ({
        isDragging: monitor.isDragging()
      })
    })

    const cellWidth = 64
    const borderWidth = 1
    const appointmentWidth = cellWidth * timeSlotSpan + borderWidth * ( timeSlotSpan - 1 )

    return (
      <div
        ref={drag as any}
        onContextMenu={onContextMenu}
        onMouseEnter={() => setHoveredId( appointment.id )}
        onMouseLeave={() => setHoveredId( null )}
        style={{
          position: isUnassigned ? "relative" : "absolute",
          background: "none",
          width: isUnassigned ? "auto" : `${appointmentWidth}px`,
          height: isUnassigned ? "auto" : `80px`,
          minWidth: isUnassigned ? "200px" : "auto",
          minHeight: isUnassigned ? "60px" : "60px",
          zIndex: zIndex,
          cursor: "pointer",
          outline: isFocused ? "" : undefined
        }}
        onClick={onClick}
        className={`rounded p-1 text-white text-xs z-10 top-0 left-0 h-full p-1 rounded text-white text-xs cursor-move ${isOverlapping ? "opacity-60" : ""} ${isUnassigned ? "relative" : "absolute"} ${isDragging ? "opacity-50" : ""} hover:shadow-lg overflow-hidden`}
        title={`${appointment.title} - ${appointment.course || ""}`}
        data-appointment-id={appointment.id}
      >
        <div
          style={{
            backgroundColor: appointment.color || "#6366f1",
            opacity: isOverlapping && !isFocused ? 0.7 : 1,
            borderRadius: "6px",
            position: "absolute",
            inset: 0,
            zIndex: 1,
            pointerEvents: "none", // so clicks pass to outer div
            border: isFocused && isOverlapping ? "2px solid #ffffff" : "none",
            boxShadow: isFocused && isOverlapping ? "0 0 0 1px rgba(99, 102, 241, 0.5)" : "none"
          }}
        >
          <div
            style={{
              position: "relative",
              zIndex: 2,
              color: "#fff",
              padding: "4px 0 0 6px",
              pointerEvents: "auto"
            }}
          >
            <div className="font-semibold truncate">
              {appointment.course || appointment.title}
            </div>
            {appointment.program && (
              <div className="text-xs opacity-80">
                {(appointment.program as any)?.label || (appointment.program as any)?.name || ""}
              </div>
            )}
            {typeof appointment.student_count === "number" && (
              <div className="text-xs opacity-70">
                {appointment.student_count} {appointment.student_count === 1 ? "student" : "students"}
              </div>
            )}

            {(() => {
              const days = appointment.times?.days?.length 
                ? appointment.times.days
                : appointment.weekdays?.length
                  ? appointment.weekdays
                  : []
              return (
                Array.isArray(days) && days.length > 0 && days.length < ABBREVIATED_DAYS_OF_WEEK.length && (
                  <div className="text-xs opacity-70">
                    {days.join(", ")}
                  </div>
                )
              )
            })()}
          </div>
        </div>
        
      </div>
    )
  }

  const DroppableCell = ({ resourceId, timeSlot }: { resourceId: string; timeSlot: string }) => {
    const dropRef = useRef<HTMLTableCellElement>(null) // specifically, for overlapping courses
    const [{ isOver }, drop] = useDrop({
      accept: "appointment",
      drop: ( item: SchedulerCanonical & { isUnassigned: boolean }, monitor) => {
        // figure out whether cursor is in the top or bottom half of this cell
        const clientOffset = monitor.getClientOffset()
        const cellRect = dropRef.current?.getBoundingClientRect()
        let snappedSlot = timeSlot // default to the cell’s own slot
        if (clientOffset && cellRect) {
          const yPos = clientOffset.y - cellRect.top
          const half = cellRect.height / 2
          if (yPos >= half) {
            // cursor is in the lower half → snap to next 30-min block
            snappedSlot = addMinutes(timeSlot, 30)
          }
        }

        let duration = 60 // default fallback
        if ( item.times.start_time && item.times.end_time ) {
          const [sh, sm] = item.times.start_time.split( ":" ).map( Number )
          const [eh, em] = item.times.end_time.split( ":" ).map( Number )
          duration = eh * 60 + em - (sh * 60 + sm)
        } else if (item.times.duration) {
          duration = typeof item.times.duration === "string"
            ? parseInt(item.times.duration, 10)
            : 60
        }

        const updatedAppointment: SchedulerCanonical = {
          ...item,
          times: {
            ...item.times,
            start_time: snappedSlot,
            end_time: addMinutes( snappedSlot, duration ),
            duration: duration.toString()
          },
          instructor: resourceType === "teacher"
            ? instructors.find( inst =>
                inst.id === resourceId ||
                inst._id === resourceId ||
                inst.uid === resourceId
              ) ?? {}
            : item.instructor,
          classroom: ( resourceType === "classroom" && rooms.find( ( room ) => room.id === resourceId )?.name )
            ? rooms.find( ( room ) => room.id === resourceId )?.name
            : rooms.find( ( room ) => room.id === resourceId )?.id
              ? rooms.find( ( room ) => room.id === resourceId )?.id
              : item.classroom
        }

        if ( item.isUnassigned ) {
          setUnassignedAppointments( ( prev ) => prev.filter( ( apt ) => apt.id !== item.id ) )
          setAppointments( ( prev ) => [...prev, updatedAppointment] )
          onAppointmentCreate?.( updatedAppointment )
        } else {
          setAppointments( ( prev ) => prev.map( ( apt ) => ( apt.id === item.id ? updatedAppointment : apt ) ) )
          onAppointmentUpdate?.( updatedAppointment )
        }
      },
      collect: ( monitor ) => ({
        isOver: monitor.isOver()
      })
    })

    drop(dropRef) // attach drop target to the ref

    // Get all appointments that start in this cell
    const cellAppointments = appointments.filter(
      (apt) =>
        ((resourceType === "teacher" ? (apt.instructor as any)?.id : apt.classroom) === resourceId) &&
        apt.times.start_time === timeSlot
    )

    // Get any appointment that occupies this cell (for continuation cells)
    const occupyingAppointments = appointments.filter((apt) => {
      const resId = resourceType === "teacher" ? (apt.instructor as any)?.id : apt.classroom
      if (resId !== resourceId || !apt.times.start_time || !apt.times.end_time) return false

      const startIndex = timeSlots.findIndex((slot) => slot.time === apt.times.start_time)
      const endIndex = timeSlots.findIndex((slot) => slot.time === apt.times.end_time)
      const currentIndex = timeSlots.findIndex((slot) => slot.time === timeSlot)

      return startIndex <= currentIndex && currentIndex < endIndex
    })

    const isStartCell = cellAppointments.length > 0
    const isLunchTime = timeSlot === "12:00" || timeSlot === "12:30"
    const cellKey = `${resourceId}-${timeSlot}`

    // Default to the first appointment if no focus set, or ensure focused appointment exists
    const focusedId = focusedAppointments[cellKey] && cellAppointments.some(a => a.id === focusedAppointments[cellKey])
      ? focusedAppointments[cellKey]
      : cellAppointments[0]?.id

    return (
      <td
        ref={dropRef}
        className={`border border-gray-300 size-16 relative ${
          isOver
            ? "bg-blue-100"
            : isLunchTime
              ? "bg-gray-200 border-white"
              : occupyingAppointments.length > 0 && !isStartCell
                ? "bg-gray-50"
                : "bg-white"
        }`}
        data-resource-id={resourceId}
        key={resourceId}
        data-time-slot={timeSlot}
        style={{
          cursor: cellAppointments.length > 1 ? "pointer" : undefined,
          position: "relative"
        }}
      >
        {/* Show continuation text only for non-start cells that have occupying appointments */}
        {occupyingAppointments.length > 0 && !isStartCell && !isLunchTime && (
          <div className="absolute inset-0 flex items-center justify-center text-xs text-gray-400 italic pointer-events-none">
            {occupyingAppointments.length === 1
              ? occupyingAppointments[0].course || occupyingAppointments[0].title
              : `${occupyingAppointments.length} courses`
            }
          </div>
        )}

        {/* Render appointments that start in this cell */}
        {isStartCell &&
          cellAppointments.map((appointment) => {
            const span = calculateTimeSlotSpan(
              appointment.times.start_time,
              appointment.times.end_time
            )
            const isFocused = appointment.id === focusedId
            const hasOverlap = cellAppointments.length > 1

            return (
              <DraggableAppointment
                key={appointment.id}
                appointment={appointment}
                timeSlotSpan={span}
                isOverlapping={hasOverlap}
                zIndex={isFocused ? 25 : (hasOverlap ? 15 : 10)}
                isFocused={isFocused}
                onClick={() =>
                  cellAppointments.length > 1 && handleCycleFocus(cellKey, cellAppointments)
                }
              />
            )
          })}

        {/* Show overlap indicator when there are multiple appointments */}
        {cellAppointments.length > 1 && (
          <div className="absolute top-1 right-1 bg-yellow-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-bold pointer-events-none z-30">
            {cellAppointments.length}
          </div>
        )}
      </td>
    )
  }

  return (
    <>
      {( dialog === "course" && open ) && (
        <ActivityDialog open={open} setOpen={setOpen} title="Create | Edit Course">
          <ActivityForm
            term={term}
            section={appointment.current}
            onSave={( result: SchedulerCanonical ) => {
              if (
                result.id 
                && ( ( result.instructor && "id" in result.instructor ) 
                || ( result.classroom && "id" in result.classroom ) ) 
                && result.times.start_time 
              ) {
                onFormUpdate?.( result )
              } else {
                onFormCreate?.( result )
              }
              setOpen( false )
            }}
            setOpen={setOpen}
            allAppointments={appointments}
          />
        </ActivityDialog>
      )}

      {( dialog === "teacher" && open ) && (
        <ActivityDialog open={open} setOpen={setOpen} title="Create | Edit Teacher">
          <AddUserForm 
            type="teacher" 
            user={appointment.current.instructor}
            edit={true}
            setOpen={setOpen} 
          />
        </ActivityDialog>
      )}

      <DndProvider backend={HTML5Backend}>
        {/* Statistics */}
        <div className="mb-4 grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-primary p-4 rounded-md">
            <h4 className="font-semibold text-white">Scheduled Classes</h4>
            <p className="text-lg font-bold text-white">{appointments.length}</p>
          </div>
          <div className="bg-yellow-100 p-4 rounded-md">
            <h4 className="font-semibold text-yellow-800">Unassigned Classes</h4>
            <p className="text-lg font-bold text-yellow-900">{unassignedAppointments.length}</p>
          </div>
          <div className="bg-green-100 p-4 rounded-md">
            <h4 className="font-semibold text-green-800">Total Teachers | Rooms</h4>
            <p className="text-lg font-bold text-green-900">{resources.length}</p>
          </div>
          <div className="bg-purple-100 p-4 rounded-md">
            <h4 className="font-semibold text-purple-800">Total Students</h4>
            <p className="text-lg font-bold text-purple-900">
              {appointments.reduce( ( sum, apt ) => sum + ( apt.student_count || 0 ), 0 )}
            </p>
          </div>
        </div>
        
        <div id="qasid-scheduler">
          {/* Scheduler Table */}
          <div ref={tableRef} className="overflow-y-scroll max-h-[1200px] border rounded-lg shadow-sm scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100">
            <table className="border-collapse border border-gray-400 min-w-max">
              <thead className="sticky top-0 z-20 bg-primary-200">
                <tr>
                  <th className="border border-gray-400 p-3 bg-primary sticky left-0 top-0 z-30 w-24">
                    <div className="font-semibold bg-primary text-white">
                      {mode === "classroom" ? ( <>Classrooms</> ) : ( <>Teachers</> )}
                    </div>
                  </th>
                  {timeSlots.map( ( slot ) => (
                    <th
                      key={slot.time}
                      className={`border border-gray-400 bg-primary text-white p-2 text-sm w-16 ${slot.time === "12:00" || slot.time === "12:30" ? "bg-gray-300" : "bg-gray-200"}`}
                    >
                      {slot.display}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {resources.map( ( resource ) => (
                  <tr key={resource.id} className="hover:bg-gray-50 z-90">
                    <td 
                      className="border border-gray-400 p-3 font-semibold bg-gray-100 text-foreground sticky left-0 w-24 h-20"
                      data-resource-id={resource.id}
                    >
                      <div className="font-medium">{resource.name}</div>
                      {resource.email && <div className="text-xs text-blue-600">{resource.email}</div>}
                      {resource.capacity && <div className="text-xs text-green-600">Cap: {resource.capacity}</div>}
                    </td>
                    {timeSlots.map( ( slot ) => (
                      <DroppableCell 
                        key={`${resource.id}-${slot.time}`} 
                        resourceId={resource.id} 
                        timeSlot={slot.time} 
                      />
                    ) )}
                  </tr>
                ) )}
              </tbody>
            </table>
          </div>

          <div className="fixed bottom-[85px] left-0 right-0 bg-gray-200 border-t border-b z-50">
            <div
              ref={scrollProxyRef}
              className="overflow-x-scroll"
              style={{ height: "16px" }} // just the scrollbar track
              onScroll={( e ) => {
                if ( tableRef.current ) {
                  tableRef.current.scrollLeft = ( e.target as HTMLDivElement ).scrollLeft
                }
              }}
            >
              <div style={{ width: tableRef.current?.scrollWidth || "100%" }} />
            </div>
          </div>
          <div className="fixed bottom-0 left-0 right-0 bg-gray-100 border-t border-gray-300 shadow-inner z-50">
            <div className="p-1">
              <div className="overflow-x-auto pb-2">
                <div className="flex gap-3 min-w-max">
                  {unassignedAppointments.length > 0 ? (
                    unassignedAppointments.map( ( appointment ) => (
                      <div key={appointment.id} className="shrink-0 min-w-[200px]">
                        <DraggableAppointment 
                          appointment={appointment} 
                          isUnassigned 
                          onContextMenu={e => {
                            e.preventDefault()
                            // Calculate position (same way as elsewhere)
                            setContextMenu({
                              show: true,
                              x: e.clientX,
                              y: e.clientY,
                              type: "appointment", // or "unassigned" if you want a different key
                              data: appointment
                            })
                          }}
                        />
                      </div>
                    ) )
                  ) : (
                    <p className="text-gray-500 italic py-4">No unassigned classes</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {contextMenu.show && (
            <>
              <div className="fixed inset-0 z-[9998] bg-black/5" onClick={handleCloseContextMenu} />
              <div
                ref={menuRef}
                className={`fixed bg-white border border-gray-300 shadow-xl z-[9999] py-1 rounded-md min-w-[150px] transition transform origin-top-left ${contextMenu.show ? "opacity-100 scale-100" : "opacity-0 scale-95"}`}
                style={{
                  left: `${menuPosition.left}px`,
                  top: `${menuPosition.top}px`
                }}
                onClick={( e ) => e.stopPropagation()}
              >
                {contextMenu.type === "appointment" ? (
                  <>
                    <button
                      className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-sm transition-colors"
                      onClick={() => handleContextMenuAction( "edit" )}
                    >
                      ✏️ Edit Class
                    </button>
                    {( contextMenu.data.classroom || !isEmptyObject( contextMenu.data.instructor ) ) ? (
                      <button
                        className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-sm transition-colors"
                          onClick={() => handleContextMenuAction( "unassign" )}
                        >
                          ↩️ Unassign {( mode === "classroom" ) ? ( <>room</> ) : ( <>teacher</> )} from class
                      </button>
                    ) : (
                      <button
                        className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-sm transition-colors"
                          onClick={() => handleContextMenuAction( "edit" )}
                        >
                          ✏️ Assign teacher or room to class
                      </button>
                    )}
                    <hr className="my-1" />
                    <button
                      className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-sm text-red-600 transition-colors"
                      onClick={() => handleContextMenuAction( "delete" )}
                    >
                      🗑️ Delete Class
                    </button>
                  </>
                ) : contextMenu.type === "cell" ? (
                  <button
                    className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-sm transition-colors"
                    onClick={() => handleContextMenuAction( "create" )}
                  >
                    ➕ Create New Class
                  </button>
                ) : contextMenu.type === "resource" ? (
                  <>
                    <button
                      className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-sm transition-colors"
                      onClick={() => handleContextMenuAction( "addResource" )}
                    >
                      ➕ Add {mode === "classroom" ? "Room" : "Teacher"}
                    </button>
                    <button
                      className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-sm text-green-600 transition-colors"
                      onClick={() => handleContextMenuAction( "editResource" )}
                    >
                      ✏️ Edit {mode === "classroom" ? "Room" : "Teacher"}
                    </button>
                    <button
                      className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-sm text-red-600 transition-colors"
                      onClick={() => handleContextMenuAction( "deleteResource" )}
                    >
                      🗑️ Delete {mode === "classroom" ? "Room" : "Teacher"}
                    </button>
                  </>
                ) : null}
                {contextMenu.type === "appointment" && (
                  <>
                    <hr className="my-1" />
                    <button
                      className="block w-full text-left px-4 py-2 hover:bg-gray-100 text-sm transition-colors"
                      onClick={() => handleContextMenuAction("duplicate")}
                    >
                      📋 Duplicate Class
                    </button>
                  </>
                )}
              </div>
            </>
          )}
        </div>
      </DndProvider>
    </>
  )
}

export default Scheduler

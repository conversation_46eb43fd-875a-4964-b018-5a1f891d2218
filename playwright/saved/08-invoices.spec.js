const { test, expect } = require( "@playwright/test" )

test.describe.serial( "pay an invoice", () => {
  let page

  test.beforeAll( async ({ browser }) => {
    page = await browser.newPage()
    await page.goto( "", { waitUntil: "networkidle" })
  })

  test.afterAll( async () => { await page.close() })

  test( "go to dashboard", async ({}) => {
    await page.waitForTimeout( 350 )
    await expect( page.getByText( "Student Information System" ) ).toBeVisible()
    await page.waitForTimeout( 350 )
  })
  
  test( "go to payments", async () => {
    await page.waitForTimeout( 350 )
    await page.goto( "/payments", { waitUntil: "networkidle" })
    await page.waitForTimeout( 350 )
  })

  test( "check that invoices are available", async () => {
    await page.waitForTimeout( 350 )
    await expect( page.locator( ".divide-y > li:nth-child(2)" ) ).toBeVisible()
    await page.waitForTimeout( 500 )
  })

  test( "pay an invoice", async () => {
    await page.waitForTimeout( 150 )
    await page.getByRole( "button", { name: "Pay Now" }).last().click()
    await page.waitForTimeout( 3500 )
  })

  test.describe.serial( "fill credit card form", () => {
    test( "fill email", async () => {
      await page.waitForTimeout( 500 )
      await page.getByRole( "textbox", { name: "Email" }).fill( "<EMAIL>" )
      await page.waitForTimeout( 500 )
    })

    test( "fill credit card number", async () => {
      await page.waitForTimeout( 500 )
      await page.getByRole( "textbox", { name: "Card number" }).fill( "****************" )
      await page.waitForTimeout( 500 )
    })

    test( "fill expiration", async () => {
      await page.waitForTimeout( 500 )
      await page.getByRole( "textbox", { name: "Expiration" }).fill( "1225" )
      await page.waitForTimeout( 500 )
    })

    test( "fill credit card cvv", async () => {
      await page.waitForTimeout( 500 )
      await page.getByRole( "textbox", { name: "CVC" }).fill( "975" )
      await page.waitForTimeout( 500 )
    })

    test( "fill name", async () => {
      await page.waitForTimeout( 500 )
      await page.getByRole( "textbox", { name: "Cardholder name" }).fill( "Noora TESTING" )
      await page.waitForTimeout( 500 )
    })

    test( "submit payment", async () => {
      await page.waitForTimeout( 500 )
      await page.getByTestId( "hosted-payment-submit-button" ).click()
      await page.waitForTimeout( 8000 )
    })
  })

  test.describe.serial( "check that the receipt is showing", () => {
    test( "check header", async () => {
      await page.waitForTimeout( 3500 )
      await expect( await page.getByText("Receipt") ).toBeVisible()
      await page.waitForTimeout( 1000 )
    })

    test( "check that the Print button is visible", async () => {
      await page.waitForTimeout( 550 )
      await expect( await page.getByRole( "button", { name: "Print" }) ).toBeVisible()
      await page.waitForTimeout( 550 )
    })

    test( "check that the total is showing", async () => {
      await page.waitForTimeout( 550 )
      await expect( await page.getByRole( "rowheader", { name: "Total", exact: true }) ).toBeVisible()
      await page.waitForTimeout( 1000 )
    })
  })
})
const { test, expect } = require( "@playwright/test" )

const makeName = ( num ) => {
  const length = num
  const characters = "abcdefghijklmnopqrstuvwxyz"
  const charactersLength = characters.length

  let result = ""
  let counter = 0
  while ( counter < length ) {
    result += characters.charAt( Math.floor( Math.random() * charactersLength ) )
    counter += 1
  }

  return result
}

test.describe.serial( "onsite admissions", () => {
  let page

  test.beforeAll( async ({ browser }) => {
    page = await browser.newPage()
    await page.goto( "", { waitUntil: "networkidle" })
  })

  test.afterAll( async () => { await page.close() })

  test( "go to dashboard", async ({}) => {
    await page.waitForTimeout( 200 )
    await expect( page.getByText( "Student Information System" ) ).toBeVisible()
    await page.waitForTimeout( 300 )
  })

  test( "has new applications link", async () => {
    await page.waitForTimeout( 1000 )
    await expect( page.getByRole("navigation").getByRole("link", { name: "New Application" }) ).toBeVisible()
    await page.waitForTimeout( 200 )
    await page.getByRole("navigation").getByRole("link", { name: "New Application" }).click()
    await page.waitForTimeout( 1000 )
  })
  
  test( "navigate to onsite form", async ({}) => {  
    await page.waitForTimeout( 800 )
    await page.getByRole("link", { name: "Qasid Onsite | Amman" }).click()
    await page.waitForTimeout( 800 )
  })

  test.describe.serial( "previous applications showing", () => {
    let application_list 

    test.beforeAll( async () => {
      await page.waitForTimeout( 800 )
      application_list = await page.getByTestId( "application-list" )
    })
  
    test( "count applications", async () => {
      if ( application_list ) {
        const elements = await page.locator( "time" )
        const list = await elements.count()
        await page.waitForTimeout( 800 )
 
        console.log( "Number of saved/submitted applications listed:", list )
      }
    })
  })

  test.describe.serial( "start a new application", () => {
    test.beforeAll( async () => {
      await page.getByTestId( "new-onsite-application-button" ).click()
      await expect( await page.getByText( /Which term are you applying for/i ) ).toBeVisible()
      await page.waitForTimeout( 1500 )
    })

    test( "select a term", async () => {
      await page.waitForTimeout( 1500 )
      await page.locator( "#terms" ).click()
      await page.waitForTimeout( 1500 )
    })

    test( "continue to next page", async () => {
      await page.waitForTimeout( 1000 )
      await page.getByRole( "button", { name: "Save & Continue" }).click()
      await page.waitForTimeout( 1000 )
    })
  })

  test.describe.serial( "fill personal info", () => {
    let savedemail
    let secondaryemail
    let dob
    let nationality
    let dialcode
    let phone
    let personalinfo

    test.beforeAll( async () => {
      await page.getByRole( "link", { name: "Back" })
      await page.waitForTimeout( 2000 )
      await expect( await page.getByText("Personal Information") ).toBeVisible()
    })

    test( "is primary email filled", async () => {
      await page.waitForTimeout( 1000 )
      savedemail = await page.locator( "input#primaryEmail" ).inputValue()
      console.log( "Check the primary email: ", savedemail )
    })

    test( "fill first name", async () => {
      if ( savedemail ) {
        const fname = makeName( 6 )
        await page.locator( "input#firstName" ).fill( fname )
      }
    })

    test( "fill last name", async () => {
      if ( savedemail ) {
        const lname = makeName( 4 )
        await page.locator( "input#lastName" ).fill( lname )
      }
    })

    test( "fill secondary email", async () => {
      if ( savedemail ) {
        const secEmail = makeName( 7 )
        secondaryemail = await page.locator( "input#secondaryEmail" )
        await secondaryemail.fill( `${secEmail}@testing.com` )

        console.log( "Check secondary email input: ", await secondaryemail.inputValue() )
      }
    })

    test( "fill dob (may not be empty)", async () => {
      if ( savedemail ) {
        dob = await page.locator( "input#dateOfBirth" )
        await dob.fill( "2000-07-11" )

        console.log( "Check dob input: ", await dob.inputValue() )
      }
    })

    test( "select nationality (may not be empty)", async () => {
      if ( savedemail ) {
        nationality = await page.locator( "select[name='nationality']" )
        nationality.selectOption({ label: "Jordan" })

        console.log( "Check nationality: ", await nationality.inputValue() )
      }
    })

    test( "fill phone number (may not be empty)", async () => {
      if ( savedemail ) {
        dialcode = await page.locator( "select[name='dialCode']" )
        dialcode.selectOption([ "GB +44" ])
        if ( dialcode ) {
          phone = await page.locator( "input#phoneNumber" )
          phone.fill( "************" )
        }
        console.log( "Check dial code: ", await dialcode.inputValue() )        
        console.log( "Check phone number: ", await phone.inputValue() )
      }
    })

    test( "click save & continue", async () => {
      await page.waitForTimeout( 1000 )

      if ( phone ) {
        await page.getByRole( "button", { name: "Save", exact: true }).click()
        await page.waitForTimeout( 1000 )

        personalinfo = await page.getByRole( "button", { name: "Save & Continue", exact: true })
        personalinfo.click()

        await page.waitForTimeout( 1000 )
        console.log( "Moving on from Personal Info: ", personalinfo )
      }
    })
  })

  test.describe.serial( "fill academic info", () => {
    test.beforeAll( async () => {
      await page.getByRole( "link", { name: "Back" })
      await page.waitForTimeout( 1000 )
      await expect( await page.getByText(/Academic Information/i) ).toBeVisible()
      await page.waitForTimeout( 1000 )
    })

    test( "select completed degree", async () => {
      await page.locator( "div:nth-child(5) > .flex" ).click()
      await page.waitForTimeout( 500 )
    })

    test( "fill degree school", async () => {
      await page.waitForTimeout( 200 )
      await page.locator( "input#degreeCompetionPlace" ).fill( "PENN UNI" )
      await page.waitForTimeout( 500 )
    })

    test( "select currently enrolled", async () => {
      await page.waitForTimeout( 200 )
      await page.locator( "div:nth-child(3) > .space-y-5 > div > .flex" ).first().click()
      await page.waitForTimeout( 500 )
    })

    test( "fill current institute", async () => {
      await page.waitForTimeout( 200 )
      await page.locator( "input#currentInstituteName" ).fill( "SOAS" )
      await page.waitForTimeout( 500 )
    })

    test( "fill areas of study", async () => {
      await page.waitForTimeout( 200 )
      await page.locator( "input#majorAndMinorAreasOfStudy" ).fill( "GEOLOGY" )
      await page.waitForTimeout( 500 )
    })

    test( "fill current occupation", async () => {
      await page.waitForTimeout( 200 )
      await page.getByLabel( "What is your current occupation?" ).fill( "LINGUISTICS" )
      await page.waitForTimeout( 500 )
    })

    test( "fill goals", async () => {
      await page.waitForTimeout( 500 )
      await page.locator( "input#futureEducationalAndProfessionalGoals" ).fill( "1. education goals 2. prof goals 3. even more goals" )
    })

    test( "select mena", async () => {
      await page.locator( "div" ).filter({ hasText: /^Have you spent time in the MENA region\?YesNo$/ }).locator( "#Yes" ).click()
      await page.waitForTimeout( 1000 )
    })

    test( "click save & continue", async () => {
      await page.waitForTimeout( 1000 )

      await page.getByRole( "button", { name: "Save", exact: true }).click()
      await page.waitForTimeout( 1000 )

      await page.getByRole( "button", { name: "Save & Continue", exact: true }).click()

      await page.waitForTimeout( 1000 )
    })
  })

  test.describe.serial( "fill previous arabic studies", () => {
    test.beforeAll( async () => {
      await page.getByRole( "link", { name: "Back" })
      await page.waitForTimeout( 1000 )
      await expect( await page.getByText(/Previous Arabic Studies/i) ).toBeVisible()
      await page.waitForTimeout( 1000 )
    })

    test( "studied at qasid before", async () => {
      await page.waitForTimeout( 500 )
      await page.locator( "div" ).filter({ hasText: /^No$/ }).nth( 1 ).click()
      await page.waitForTimeout( 1500 )
    })

    test( "input years of arabic study", async () => {
      await page.locator( "input#yearsOfArabicStudy" ).fill( "5" )
      await page.waitForTimeout( 500 )
    })

    test( "list books", async () => {
      await page.locator( "textarea#booksUsedInfo" ).fill( "Books on kitab al arabiya" )
      await page.waitForTimeout( 500 )
    })

    test( "select al-kitaab series", async () => {
      await page.getByLabel( "I studied from the 3rd edition of the Al Kitaab series and covered both the MSA and dialect portions" ).click()
      await page.waitForTimeout( 500 )
    })

    test( "select yes to dialect", async () => {
      await page.waitForTimeout( 500 )
      await page.locator( "div:nth-child(6) > .space-y-5 > div > .flex" ).first().click()
      await page.waitForTimeout( 500 )
    })

    test( "fill dialects", async () => {
      await page.waitForTimeout( 500 )
      await page.getByTestId( "dialectsDescription" ).fill( "Egyptian, Jordanian, Lebanese" )
      await page.waitForTimeout( 500 )
    })

    test( "fill in estimated years", async () => {
      await page.waitForTimeout( 500 )
      await page.getByPlaceholder( /Enter an estimated number of/ ).fill( "2 years, 5 months, 4 days, 12 hours, 12 minutes" )
      await page.waitForTimeout( 500 )
    })

    test( "click save & continue", async () => {
      await page.waitForTimeout( 200 )

      await page.getByRole( "button", { name: "Save", exact: true }).click()
      await page.waitForTimeout( 200 )

      await page.getByRole( "button", { name: "Save & Continue", exact: true }).click()
      await page.waitForTimeout( 1500 )
    })
  })

  test.describe.serial( "fill onsite in amman", () => {
    test.beforeAll( async () => {
      await page.getByRole( "link", { name: "Back" })
      await page.waitForTimeout( 1000 )
      await expect( await page.getByText(/Onsite in Amman/i) ).toBeVisible()
      await page.waitForTimeout( 500 )
    })

    test( "select program", async () => {
      await page.locator( "div" ).filter({ hasText: /^Classical Arabic \| Core Program$/ }).first().click()
      await page.waitForTimeout( 500 )
    })

    test( "select supplementary classes", async () => {
      await page.locator( "div:nth-child(2) > .space-y-5 > div > .flex" ).first().click()
      await page.waitForTimeout( 500 )
      await page.getByLabel( "Arabic Cooking Class" ).click()
      await page.waitForTimeout( 500 )
      await page.getByLabel( "Grammar Intensive" ).click()
      await page.waitForTimeout( 500 )
    })

    test( "select private tutoring", async () => {
      await page.getByLabel( "Yes" ).click()
      await page.waitForTimeout( 500 )
    })

    test( "fill private tutoring hours", async () => {
      await page.locator( "input#privateTutoringWeeklyHours" ).fill( "30" )
      await page.waitForTimeout( 500 )
    })

    test( "fill commencement date", async () => {
      await page.locator( "input#dateToCommencePrivateTutoring" ).fill( "2024-07-11" )
      await page.waitForTimeout( 500 )
    })

    test( "fill completion date", async () => {
      await page.locator( "input#dateToCompletePrivateTutoring" ).fill( "2024-09-15" )
      await page.waitForTimeout( 500 )
    })

    test( "select co-applicants", async () => {
      await page.locator( "div" ).filter({ hasText: /^Are you applying with co-applicants\?YesNo$/ }).locator( "#Yes" ).click()
      await page.waitForTimeout( 500 )
    })

    test( "fill co-applicants", async () => {
      await page.locator( "textarea#coapplicantNameAndEmail" ).fill( "name: email, name: email" )
      await page.waitForTimeout( 500 )
    })

    test( "click save & continue", async () => {
      await page.getByRole( "button", { name: "Save", exact: true }).click()
      await page.waitForTimeout( 500 )

      await page.getByRole( "button", { name: "Save & Continue", exact: true }).click()
      await page.waitForTimeout( 1000 )
    })
  })

  test.describe.serial( "fill onsite in amman | funding", () => {
    test.beforeAll( async () => {
      await page.getByRole( "link", { name: "Back" })
      await page.waitForTimeout( 1500 )
      await expect( await page.getByText(/Onsite in Amman | Funding/i).first() ).toBeVisible()
      await page.waitForTimeout( 500 )
    })

    test( "select receiving funding", async () => {
      await page.waitForTimeout( 1500 )
      await page.locator( ".space-y-5 > div > .flex" ).first().click()
      await page.waitForTimeout( 500 )
    })

    test( "fill funding org", async () => {
      await page.waitForTimeout( 1500 )
      await page.locator( "input#fundingProviderName" ).fill( "Funding name" )
      await page.waitForTimeout( 500 )
    })

    test( "select minimum hours", async () => {
      await page.waitForTimeout( 1500 )
      await page.getByRole( "main" ).locator( "form div" ).filter({ hasText: "Does your university, employer, or funding source require a minimum number of we" }).locator( "#Yes" ).click()
      await page.waitForTimeout( 500 )
    })

    test( "fill minimum hours", async () => {
      await page.waitForTimeout( 1500 )
      await page.locator( "input#requiredWeeklySynchronousTeachingHours" ).fill( "10" )
      await page.waitForTimeout( 500 )
    })

    test( "select syllabus approval", async () => {
      await page.waitForTimeout( 1500 )
      await page.getByRole( "main" ).locator( "form div" ).filter({ hasText: "Does your university, employer, or source of funding require a sample syllabus i" }).locator( "#Yes" ).click()
      await page.waitForTimeout( 500 )
    })

    test( "fill syllaus email", async () => {
      await page.waitForTimeout( 1500 )
      await page.locator( "input#emailToSendRequiredSampleSyllabus" ).fill( "<EMAIL>" )
      await page.waitForTimeout( 500 )
    })
    
    test( "click save & continue", async () => {
      await page.waitForTimeout( 1500 )
      await page.getByRole( "button", { name: "Save", exact: true }).click()
      await page.waitForTimeout( 500 )

      await page.getByRole( "button", { name: "Save & Continue", exact: true }).click()
      await page.waitForTimeout( 500 )
    })
  })

  test.describe.serial( "fill onsite in amman | objectives", () => {
    test.beforeAll( async () => {
      await page.waitForTimeout( 1500 )
      await page.getByRole( "link", { name: "Back" })
      await page.waitForTimeout( 500 )
      await expect( await page.getByText(/Onsite in Amman | Objectives/i).first() ).toBeVisible()
      await page.waitForTimeout( 500 )
    })

    test( "fill the why", async () => {
      await page.locator( "textarea#whyDoYouWantToLearnArabic" ).fill( "Nam non facilisis diam. Duis vitae feugiat tellus. Nulla iaculis erat augue, eu gravida ligula imperdiet et. Maecenas vel purus ipsum. Sed sit amet odio ut mi mollis interdum. Aliquam erat volutpat." 
      )
      await page.waitForTimeout( 500 )
    })

    test( "fill the objectives", async () => {
      await page.locator( "textarea#objectivesAchievedFromStudiedAtQasid" ).fill( "Nunc facilisis sagittis egestas. Pellentesque sit amet nunc non justo lobortis auctor. Nullam rutrum cursus sem non porttitor" )
      await page.waitForTimeout( 500 )
    })

    test( "click save & continue", async () => {
      await page.getByRole( "button", { name: "Save", exact: true }).click()
      await page.waitForTimeout( 500 )

      await page.getByRole( "button", { name: "Save & Continue", exact: true }).click()
      await page.waitForTimeout( 500 )
    })
  })

  test.describe.serial( "fill onsite in amman | general questions", () => {
    test.beforeAll( async () => {
      await page.getByRole( "link", { name: "Back" })
      await page.waitForTimeout( 1000 )
      await expect( await page.getByText(/Onsite in Amman | General Questions/i).first() ).toBeVisible()
      await page.waitForTimeout( 500 )
    })

    test( "select zoom meeting", async () => {
      await page.waitForTimeout( 200 )
      await page.getByLabel( "Yes" ).click()
      await page.waitForTimeout( 500 )
    })

    test( "check zoom info", async () => {
      await page.waitForTimeout( 200 )
      const element = await page.getByText( "ZOOM CALL WITH A STAFF MEMBER" )
      expect( element !== undefined ).toBeTruthy()
      await page.waitForTimeout( 500 )
    })

    test( "fill the questions & comments", async () => {
      await page.waitForTimeout( 200 )
      await page.locator( "textarea#additionalCommentsOrQuestions" ).fill( "Nullam rutrum cursus sem non porttitor" )
      await page.waitForTimeout( 500 )
    })

    test( "click save & continue", async () => {
      await page.waitForTimeout( 200 )
      await page.getByRole( "button", { name: "Save", exact: true }).click()
      await page.waitForTimeout( 500 )

      await page.getByRole( "button", { name: "Save & Continue", exact: true }).click()
      await page.waitForTimeout( 500 )
    })
  })

  test.describe.serial( "upload current resume", () => {
    test.beforeAll( async () => {
      await page.getByRole( "link", { name: "Back" })
      await page.waitForTimeout( 1000 )
      await expect( await page.getByText(/Upload Current Resume | Curriculum Vitae/i).first() ).toBeVisible()
      await page.waitForTimeout( 1000 )
    })

    test( "select a file", async () => {
      await page.waitForTimeout( 200 )
      await page.locator( 'input[name="resume"]' ).setInputFiles( "/Users/<USER>/Downloads/upload.doc" )
      await page.waitForTimeout( 200 )
    })

    test( "click upload button", async () => {
      await page.waitForTimeout( 200 )
      await page.getByRole( "button", { name: "Upload", exact: true }).click()
      await page.waitForTimeout( 1500 )
    })

    test( "check that file was uploaded", async () => {
      await page.waitForTimeout( 200 )
      const element = await page.getByText( "File successfully uploaded!" )
      expect( element !== undefined ).toBeTruthy()
      await page.waitForTimeout( 500 )
    })

    test( "select a file again", async () => {
      await page.waitForTimeout( 200 )
      await page.locator( 'input[name="resume"]' ).setInputFiles( "/Users/<USER>/Downloads/upload.pdf" )
      await page.waitForTimeout( 1000 )
    })

    test( "click upload button again", async () => {
      await page.getByRole( "button", { name: "Upload", exact: true }).click()
      await page.waitForTimeout( 2000 )
    })

    test( "check that file was uploaded again", async () => {
      await page.waitForTimeout( 500 )
      expect( await page.getByText( "File successfully uploaded!" ) ).toBeVisible()
      await page.waitForTimeout( 1000 )
    })

    test( "click save & continue", async () => {
      await page.waitForTimeout( 500 )
      await page.getByRole( "button", { name: "Save & Continue", exact: true }).click()
      await page.waitForTimeout( 1500 )
    })
  })

  test.describe.serial( "request recommendation letter", () => {
    test.beforeAll( async () => {
      await page.getByRole( "link", { name: "Back" })
      await page.waitForTimeout( 1000 )
      await expect( await page.getByText(/Request a Letter of Recommendation/i).first() ).toBeVisible()
      await page.waitForTimeout( 2500 )
    })

    test( "fill reference name", async () => {
      await page.waitForTimeout( 800 )
      const profName = makeName( 6 )
      await page.locator( "input#referenceName" ).fill( "Dr. " + profName + " PhD" )
      await page.waitForTimeout( 500 )
    })

    test( "fill reference email", async () => {
      await page.waitForTimeout( 800 )
      const profEmail = makeName( 4 )
      await page.locator( "input#referenceEmail" ).fill( `${profEmail}@doctors.com` )
      await page.waitForTimeout( 500 )
    })

    test( "click save & continue", async () => {
      await page.waitForTimeout( 800 )
      await page.getByRole( "button", { name: "Save & Continue", exact: true }).click()
      await page.waitForTimeout( 500 )
    })
  })

  test.describe.serial( "complete application", () => {
    test.beforeAll( async () => {
      await page.waitForSelector( "button:has-text('Submit Your Application')" )
      await page.waitForTimeout( 1000 )
      await expect( await page.getByText(/Your application is nearly complete/i).first() ).toBeVisible()
      await page.waitForTimeout( 500 )
    })

    test( "submit application", async () => {
      await page.waitForTimeout( 200 )
      await page.getByRole( "button", { name: "Submit Your Application", exact: true }).click()
      await page.waitForTimeout( 500 )
    })
  })

  test.describe.serial( "completed application info", () => {
    test.beforeAll( async () => {
      await page.waitForSelector( "button:has-text('Back to Admissions Dashboard')" )
      await page.waitForTimeout( 1000 )
      await expect( await page.getByRole( "heading", { name: /Thank you/i }).first() ).toBeVisible()
      await page.waitForTimeout( 1000 )
    })

    test( "check that message is viewable", async () => {
      await page.waitForTimeout( 200 )
      const element = await page.getByText( "<EMAIL>" )
      expect( element !== undefined ).toBeTruthy()
    })

    test( "return to admissions dashboard", async () => {
      await page.waitForTimeout( 200 )
      await page.getByRole( "button", { name: "Back to Admissions Dashboard", exact: true }).click()
      await page.waitForTimeout( 500 )
    })
  })

  test.describe.serial( "check application is showing on dashboard", () => {
    test.beforeAll( async () => {
      await page.waitForTimeout( 1500 )
      await page.waitForSelector( "button:has-text('Start New Application')" )
      await page.waitForTimeout( 1000 )
      await expect( await page.getByTestId( "application-list" ).locator( "div" ).nth( 1 ) ).toBeVisible()
      await page.waitForTimeout( 1000 )
    })

    test( "check that application is available", async () => {
      await expect( await page.getByTestId( "application-list" ).locator( "div" ).nth(1) ).toBeVisible()
    })
  })
})